"""
MCP Integration Tests
Tests for the Model Context Protocol integration with Motion Agent
"""

import pytest
import asyncio
import json
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from backend.blender.mcp_server import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MCPConnection
from backend.blender.mcp_enhanced_converter import MCPEnhancedConverter
from backend.blender.addon_manager import BlenderAddonManager
from backend.blender.blender_config import BlenderConfig


class TestMCPConnection:
    """Test MCP connection functionality"""
    
    def test_connection_initialization(self):
        """Test MCP connection initialization"""
        conn = MCPConnection()
        assert conn.host == "localhost"
        assert conn.port == 9876
        assert conn.connected is False
        assert conn.sock is None
    
    def test_connection_custom_params(self):
        """Test MCP connection with custom parameters"""
        conn = MCPConnection(host="127.0.0.1", port=8888)
        assert conn.host == "127.0.0.1"
        assert conn.port == 8888
    
    @patch('socket.socket')
    def test_successful_connection(self, mock_socket):
        """Test successful connection to MCP server"""
        mock_sock = Mock()
        mock_socket.return_value = mock_sock
        
        conn = MCPConnection()
        result = conn.connect()
        
        assert result is True
        assert conn.connected is True
        assert conn.sock == mock_sock
        mock_sock.connect.assert_called_once_with(("localhost", 9876))
    
    @patch('socket.socket')
    def test_failed_connection(self, mock_socket):
        """Test failed connection to MCP server"""
        mock_sock = Mock()
        mock_sock.connect.side_effect = ConnectionRefusedError("Connection refused")
        mock_socket.return_value = mock_sock
        
        conn = MCPConnection()
        result = conn.connect()
        
        assert result is False
        assert conn.connected is False
        assert conn.sock is None
    
    def test_disconnect(self):
        """Test disconnection from MCP server"""
        conn = MCPConnection()
        conn.sock = Mock()
        conn.connected = True
        
        conn.disconnect()
        
        assert conn.connected is False
        assert conn.sock is None


class TestMCPServerManager:
    """Test MCP server manager functionality"""
    
    def test_manager_initialization(self):
        """Test MCP server manager initialization"""
        manager = MCPServerManager()
        assert manager.connection is not None
        assert manager.server_process is None
        assert manager.addon_installed is False
    
    @patch('subprocess.Popen')
    @patch('time.sleep')
    def test_start_server_success(self, mock_sleep, mock_popen):
        """Test successful server startup"""
        mock_process = Mock()
        mock_popen.return_value = mock_process
        
        manager = MCPServerManager()
        
        # Mock successful connection after startup
        with patch.object(manager.connection, 'connect', return_value=True):
            result = manager.start_server()
        
        assert result is True
        assert manager.server_process == mock_process
        mock_popen.assert_called_once_with(
            ["uvx", "blender-mcp"],
            stdout=mock_popen.return_value.stdout,
            stderr=mock_popen.return_value.stderr,
            text=True
        )
    
    @patch('subprocess.Popen')
    @patch('time.sleep')
    def test_start_server_already_running(self, mock_sleep, mock_popen):
        """Test starting server when already running"""
        manager = MCPServerManager()
        
        # Mock server already running
        with patch.object(manager.connection, 'connect', return_value=True):
            result = manager.start_server()
        
        assert result is True
        mock_popen.assert_not_called()
    
    def test_stop_server(self):
        """Test stopping MCP server"""
        manager = MCPServerManager()
        mock_process = Mock()
        manager.server_process = mock_process
        
        with patch.object(manager.connection, 'disconnect'):
            manager.stop_server()
        
        mock_process.terminate.assert_called_once()
        assert manager.server_process is None
    
    def test_get_scene_info_success(self):
        """Test successful scene info retrieval"""
        manager = MCPServerManager()
        expected_info = {
            "name": "Scene",
            "object_count": 3,
            "objects": [
                {"name": "Cube", "type": "MESH", "location": [0, 0, 0], "visible": True}
            ]
        }
        
        with patch.object(manager.connection, 'send_command', return_value=expected_info):
            result = manager.get_scene_info()
        
        assert result == expected_info
    
    def test_get_scene_info_error(self):
        """Test scene info retrieval with error"""
        manager = MCPServerManager()
        
        with patch.object(manager.connection, 'send_command', side_effect=Exception("Connection error")):
            result = manager.get_scene_info()
        
        assert "error" in result
        assert result["error"] == "Connection error"
    
    def test_execute_blender_code_success(self):
        """Test successful Blender code execution"""
        manager = MCPServerManager()
        test_code = "import bpy; print('Hello from Blender')"
        expected_result = "Hello from Blender"
        
        with patch.object(manager.connection, 'send_command', return_value=expected_result):
            result = manager.execute_blender_code(test_code)
        
        assert result["success"] is True
        assert result["result"] == expected_result
    
    def test_execute_blender_code_error(self):
        """Test Blender code execution with error"""
        manager = MCPServerManager()
        test_code = "invalid python code"
        
        with patch.object(manager.connection, 'send_command', side_effect=Exception("Syntax error")):
            result = manager.execute_blender_code(test_code)
        
        assert result["success"] is False
        assert "error" in result


class TestMCPEnhancedConverter:
    """Test MCP enhanced converter functionality"""
    
    def test_converter_initialization_with_mcp(self):
        """Test converter initialization with MCP enabled"""
        with patch('backend.blender.mcp_enhanced_converter.get_mcp_manager') as mock_get_manager:
            mock_manager = Mock()
            mock_manager.is_server_running.return_value = True
            mock_get_manager.return_value = mock_manager
            
            converter = MCPEnhancedConverter(use_mcp=True)
            
            assert converter.use_mcp is True
            assert converter.mcp_manager == mock_manager
    
    def test_converter_initialization_without_mcp(self):
        """Test converter initialization with MCP disabled"""
        converter = MCPEnhancedConverter(use_mcp=False)
        
        assert converter.use_mcp is False
        assert converter.mcp_manager is None
    
    def test_converter_fallback_on_mcp_failure(self):
        """Test converter fallback when MCP fails"""
        with patch('backend.blender.mcp_enhanced_converter.get_mcp_manager') as mock_get_manager:
            mock_get_manager.side_effect = Exception("MCP not available")
            
            converter = MCPEnhancedConverter(use_mcp=True)
            
            assert converter.use_mcp is False
    
    def test_convert_actions_with_mcp(self):
        """Test action conversion with MCP"""
        with patch('backend.blender.mcp_enhanced_converter.get_mcp_manager') as mock_get_manager:
            mock_manager = Mock()
            mock_manager.is_server_running.return_value = True
            mock_manager.execute_blender_code.return_value = {"success": True, "result": "OK"}
            mock_manager.get_scene_info.return_value = {
                "name": "Scene",
                "object_count": 1,
                "objects": []
            }
            mock_get_manager.return_value = mock_manager
            
            converter = MCPEnhancedConverter(use_mcp=True)
            
            test_actions = [
                {
                    "action": "move",
                    "params": {
                        "direction": "forward",
                        "speed": 1.0,
                        "duration": 2.0
                    }
                }
            ]
            
            result = converter.convert_actions_to_blender(test_actions, "TestProject")
            
            assert result.project_name == "TestProject"
            assert result.metadata["mcp_enhanced"] is True
            assert len(result.animation_clips) > 0
    
    def test_convert_actions_fallback_to_basic(self):
        """Test action conversion fallback to basic converter"""
        converter = MCPEnhancedConverter(use_mcp=False)
        
        test_actions = [
            {
                "action": "idle",
                "params": {"duration": 1.0}
            }
        ]
        
        # This should use the parent class method
        result = converter.convert_actions_to_blender(test_actions, "TestProject")
        
        assert result.project_name == "TestProject"
        assert result.metadata.get("mcp_enhanced") is not True


class TestBlenderAddonManager:
    """Test Blender addon manager functionality"""
    
    def test_addon_manager_initialization(self):
        """Test addon manager initialization"""
        manager = BlenderAddonManager()
        assert manager.addon_name == "motion_agent_mcp"
        assert "Motion Agent MCP" in manager.addon_bl_info["name"]
    
    def test_get_addon_source_path_configured(self):
        """Test getting addon source path from configuration"""
        with tempfile.NamedTemporaryFile(suffix=".py", delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            mock_config = Mock()
            mock_config.get_addon_path.return_value = tmp_path
            
            manager = BlenderAddonManager(config=mock_config)
            result = manager.get_addon_source_path()
            
            assert result == tmp_path
        finally:
            os.unlink(tmp_path)
    
    def test_get_addon_source_path_default(self):
        """Test getting default addon source path"""
        mock_config = Mock()
        mock_config.get_addon_path.return_value = None
        
        manager = BlenderAddonManager(config=mock_config)
        
        # Mock the default path existence check
        with patch('os.path.exists', return_value=True):
            result = manager.get_addon_source_path()
            assert result is not None
            assert result.endswith("blender_mcp_addon.py")
    
    @patch('os.makedirs')
    @patch('shutil.copy2')
    @patch('os.path.exists', return_value=True)
    def test_install_addon_success(self, mock_exists, mock_copy, mock_makedirs):
        """Test successful addon installation"""
        manager = BlenderAddonManager()
        
        with patch.object(manager, 'get_addon_source_path', return_value="/source/addon.py"):
            with patch.object(manager, 'get_blender_addon_directories', return_value=["/target/addons"]):
                with patch.object(manager, '_enable_addon_via_mcp', return_value={"success": True}):
                    result = manager.install_addon()
        
        assert result["success"] is True
        assert "/target/addons/motion_agent_mcp.py" in result["target_path"]
        mock_copy.assert_called_once()
    
    def test_install_addon_no_source(self):
        """Test addon installation with no source file"""
        manager = BlenderAddonManager()
        
        with patch.object(manager, 'get_addon_source_path', return_value=None):
            result = manager.install_addon()
        
        assert result["success"] is False
        assert "未找到插件源文件" in result["error"]
    
    def test_check_addon_status(self):
        """Test checking addon status"""
        manager = BlenderAddonManager()
        
        with patch.object(manager, 'get_blender_addon_directories', return_value=["/addons"]):
            with patch('os.path.exists', return_value=True):
                with patch.object(manager, '_check_addon_enabled_via_mcp', 
                                return_value={"enabled": True, "message": "Enabled"}):
                    result = manager.check_addon_status()
        
        assert result["installed"] is True
        assert result["enabled"] is True
        assert len(result["installed_paths"]) > 0


class TestBlenderConfigMCP:
    """Test Blender configuration MCP extensions"""
    
    def test_mcp_settings_default(self):
        """Test default MCP settings"""
        config = BlenderConfig()
        mcp_settings = config.get_mcp_settings()
        
        assert mcp_settings["enabled"] is True
        assert mcp_settings["server_host"] == "localhost"
        assert mcp_settings["server_port"] == 9876
        assert "features" in mcp_settings
    
    def test_update_mcp_settings(self):
        """Test updating MCP settings"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
            json.dump({}, tmp_file)
            config_path = tmp_file.name
        
        try:
            config = BlenderConfig(config_path=config_path)
            
            new_settings = {
                "server_port": 9999,
                "enabled": False
            }
            
            config.update_mcp_settings(new_settings)
            
            # Verify settings were updated
            mcp_settings = config.get_mcp_settings()
            assert mcp_settings["server_port"] == 9999
            assert mcp_settings["enabled"] is False
        finally:
            os.unlink(config_path)
    
    def test_mcp_server_config(self):
        """Test MCP server configuration"""
        config = BlenderConfig()
        
        server_config = config.get_mcp_server_config()
        
        assert "host" in server_config
        assert "port" in server_config
        assert "timeout" in server_config
        assert "max_retries" in server_config
    
    def test_set_mcp_feature(self):
        """Test setting MCP feature flags"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
            json.dump({}, tmp_file)
            config_path = tmp_file.name
        
        try:
            config = BlenderConfig(config_path=config_path)
            
            config.set_mcp_feature("scene_info", False)
            config.set_mcp_feature("viewport_screenshot", True)
            
            features = config.get_mcp_features()
            assert features["scene_info"] is False
            assert features["viewport_screenshot"] is True
        finally:
            os.unlink(config_path)


@pytest.mark.asyncio
class TestMCPIntegrationE2E:
    """End-to-end integration tests"""
    
    async def test_full_mcp_workflow(self):
        """Test complete MCP workflow"""
        # This would be a comprehensive test that:
        # 1. Starts MCP server
        # 2. Installs addon
        # 3. Converts actions using MCP
        # 4. Exports FBX
        # 5. Cleans up
        
        # For now, this is a placeholder for future implementation
        # when we have a test Blender environment
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
