#!/usr/bin/env python3
"""
Motion Agent 角色集成功能演示
展示从自然语言到FBX文件的完整流程，包含角色信息的一致性传递
"""

import json
from backend.agent.animation_agent import GameActionAgent
from backend.blender.fbx_exporter import FBXExporter
from backend.characters.character_manager import character_manager


def demo_character_integration():
    """演示角色集成功能"""
    print("🎭 Motion Agent 角色集成功能演示")
    print("=" * 60)
    
    # 创建代理和导出器
    agent = GameActionAgent()
    exporter = FBXExporter()
    
    # 演示用例
    demo_cases = [
        {
            "input": "勇敢的战士举起盾牌防御，然后挥剑攻击敌人",
            "expected_character": "warrior",
            "description": "战士角色的战斗动作序列"
        },
        {
            "input": "小女孩开心地蹦蹦跳跳，然后向妈妈挥手",
            "expected_character": "child", 
            "description": "儿童角色的活泼动作"
        },
        {
            "input": "机器人缓慢启动，然后机械地向前行走",
            "expected_character": "robot",
            "description": "机器人角色的机械动作"
        },
        {
            "input": "优雅的精灵轻盈地跳跃，在空中旋转",
            "expected_character": "fantasy_elf",
            "description": "精灵角色的优雅动作"
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n🎬 演示 {i}: {case['description']}")
        print(f"📝 输入: {case['input']}")
        print("-" * 50)
        
        # 1. 自然语言解析
        print("🔍 步骤 1: 解析自然语言...")
        parse_result = agent.parse_natural_language(case["input"])
        
        if not parse_result["success"]:
            print(f"❌ 解析失败: {parse_result.get('error')}")
            continue
        
        character_info = parse_result.get("character")
        actions = parse_result.get("actions", [])
        
        print(f"✅ 解析成功!")
        print(f"   角色类型: {character_info['type']}")
        print(f"   角色名称: {character_info['name']}")
        print(f"   动作数量: {len(actions)}")
        
        # 验证角色类型是否符合预期
        if character_info['type'] == case['expected_character']:
            print(f"   ✅ 角色类型匹配预期: {case['expected_character']}")
        else:
            print(f"   ⚠️  角色类型不匹配: 期望 {case['expected_character']}, 实际 {character_info['type']}")
        
        # 2. 角色配置获取
        print("\n🎨 步骤 2: 获取角色配置...")
        character_config = character_manager.get_character_type(character_info['type'])
        if character_config:
            print(f"   角色显示名: {character_config['display_name']}")
            print(f"   身高: {character_config['height']}m")
            print(f"   行走风格: {character_config['animation_features']['walking_style']}")
            print(f"   跳跃风格: {character_config['animation_features']['jump_style']}")
        
        # 3. 动作详情
        print("\n🎯 步骤 3: 动作序列分析...")
        for j, action in enumerate(actions, 1):
            action_type = action.get('action_type', 'unknown')
            description = action.get('description_zh', action.get('description_en', 'N/A'))
            print(f"   动作 {j}: {action_type} - {description}")
        
        # 4. FBX导出
        print("\n🎬 步骤 4: 导出FBX文件...")
        output_filename = f"demo_{character_info['type']}_{i}.fbx"
        
        export_result = exporter.export_actions_to_fbx(
            actions=actions,
            output_path=output_filename,
            project_name=f"Demo_{character_info['type'].title()}_{i}",
            character_name=character_info.get("name", "DemoCharacter"),
            character_info=character_info
        )
        
        if export_result["success"]:
            print(f"   ✅ FBX导出成功!")
            print(f"   📁 文件路径: {export_result['output_path']}")
            print(f"   📊 文件大小: {export_result['file_size']:,} 字节")
            
            # 验证角色信息是否正确传递
            blender_project = export_result.get("blender_project", {})
            metadata = blender_project.get("metadata", {})
            
            if "character" in metadata and "character_config" in metadata:
                saved_character = metadata["character"]
                saved_config = metadata["character_config"]
                
                print(f"   ✅ 角色信息已保存:")
                print(f"      类型: {saved_character['type']}")
                print(f"      名称: {saved_character['name']}")
                print(f"      配置: {saved_config['display_name']} (身高: {saved_config['height']}m)")
            else:
                print(f"   ⚠️  角色信息未正确保存")
        else:
            print(f"   ❌ FBX导出失败: {export_result.get('error')}")
        
        print("\n" + "=" * 60)
    
    # 总结
    print("\n🎉 演示完成!")
    print("\n📋 功能总结:")
    print("✅ 自然语言解析 - 自动识别角色类型和动作序列")
    print("✅ 角色配置管理 - 支持多种角色类型和自定义属性")
    print("✅ 动作转换 - 将抽象动作转换为具体的动画数据")
    print("✅ FBX导出 - 生成包含角色信息的完整FBX文件")
    print("✅ 一致性保证 - 角色信息在整个流程中保持一致")
    
    print("\n📁 生成的文件:")
    print("   - FBX文件: 包含角色模型和动画数据")
    print("   - JSON文件: 包含完整的项目元数据和角色配置")
    
    print("\n🔧 支持的角色类型:")
    for char_type in character_manager.get_all_character_types():
        print(f"   - {char_type['name']}: {char_type['display_name']} (身高: {char_type['height']}m)")


def demo_character_comparison():
    """演示不同角色类型的差异"""
    print("\n🔍 角色类型对比演示")
    print("=" * 60)
    
    # 相同动作，不同角色
    base_action = "角色向前走3步然后跳跃"
    character_prefixes = [
        ("强壮的战士", "warrior"),
        ("活泼的小女孩", "child"),
        ("精密的机器人", "robot"),
        ("优雅的精灵", "fantasy_elf")
    ]
    
    agent = GameActionAgent()
    
    for prefix, expected_type in character_prefixes:
        input_text = f"{prefix}{base_action[2:]}"  # 替换"角色"为具体角色
        print(f"\n🎭 测试: {input_text}")
        
        result = agent.parse_natural_language(input_text)
        if result["success"]:
            character_info = result.get("character")
            actions = result.get("actions", [])
            
            print(f"   角色类型: {character_info['type']} ({'✅' if character_info['type'] == expected_type else '❌'})")
            print(f"   角色名称: {character_info['name']}")
            print(f"   动作数量: {len(actions)}")
            
            # 获取角色特征
            char_config = character_manager.get_character_type(character_info['type'])
            if char_config:
                features = char_config['animation_features']
                print(f"   动画特征: 行走={features['walking_style']}, 跳跃={features['jump_style']}")
        else:
            print(f"   ❌ 解析失败: {result.get('error')}")


def main():
    """主函数"""
    try:
        demo_character_integration()
        demo_character_comparison()
        
        print("\n🎊 所有演示完成!")
        print("现在您可以在 backend/output/ 目录中查看生成的FBX文件。")
        print("这些文件可以直接导入到Blender、Unity、Maya等3D软件中使用。")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
