[{"name": "human_male", "display_name": "男性人类", "display_name_en": "Human Male", "description": "标准男性人类角色", "description_en": "Standard male human character", "body_type": "humanoid", "height": 1.8, "proportions": {"head_scale": 1.0, "torso_scale": [0.8, 0.4, 1.2], "arm_scale": [0.3, 0.3, 1.8], "leg_scale": [0.35, 0.35, 1.6]}, "material": {"skin_color": [0.8, 0.6, 0.4, 1.0], "clothing_color": [0.2, 0.3, 0.8, 1.0]}, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "confident", "jump_style": "athletic"}}, {"name": "human_female", "display_name": "女性人类", "display_name_en": "Human Female", "description": "标准女性人类角色", "description_en": "Standard female human character", "body_type": "humanoid", "height": 1.65, "proportions": {"head_scale": 0.95, "torso_scale": [0.7, 0.35, 1.1], "arm_scale": [0.28, 0.28, 1.7], "leg_scale": [0.32, 0.32, 1.55]}, "material": {"skin_color": [0.85, 0.65, 0.45, 1.0], "clothing_color": [0.8, 0.2, 0.4, 1.0]}, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "graceful", "jump_style": "elegant"}}, {"name": "warrior", "display_name": "战士", "display_name_en": "Warrior", "description": "强壮的战士角色", "description_en": "Strong warrior character", "body_type": "humanoid", "height": 1.9, "proportions": {"head_scale": 1.1, "torso_scale": [1.0, 0.5, 1.4], "arm_scale": [0.4, 0.4, 2.0], "leg_scale": [0.4, 0.4, 1.8]}, "material": {"skin_color": [0.7, 0.5, 0.3, 1.0], "clothing_color": [0.4, 0.2, 0.1, 1.0]}, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "heavy", "jump_style": "powerful"}}, {"name": "child", "display_name": "儿童", "display_name_en": "Child", "description": "儿童角色", "description_en": "Child character", "body_type": "humanoid", "height": 1.2, "proportions": {"head_scale": 1.3, "torso_scale": [0.6, 0.3, 0.8], "arm_scale": [0.2, 0.2, 1.2], "leg_scale": [0.25, 0.25, 1.0]}, "material": {"skin_color": [0.9, 0.7, 0.5, 1.0], "clothing_color": [0.9, 0.8, 0.2, 1.0]}, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "bouncy", "jump_style": "playful"}}, {"name": "robot", "display_name": "机器人", "display_name_en": "Robot", "description": "机械角色", "description_en": "Mechanical character", "body_type": "humanoid", "height": 2.0, "proportions": {"head_scale": 0.8, "torso_scale": [0.9, 0.6, 1.3], "arm_scale": [0.35, 0.35, 1.9], "leg_scale": [0.38, 0.38, 1.7]}, "material": {"skin_color": [0.6, 0.6, 0.7, 1.0], "clothing_color": [0.3, 0.3, 0.3, 1.0]}, "animation_features": {"has_facial_expressions": false, "has_hand_gestures": true, "walking_style": "mechanical", "jump_style": "robotic"}}, {"name": "fantasy_elf", "display_name": "精灵", "display_name_en": "<PERSON><PERSON>", "description": "优雅的精灵角色", "description_en": "Graceful elf character", "body_type": "humanoid", "height": 1.75, "proportions": {"head_scale": 0.9, "torso_scale": [0.7, 0.3, 1.15], "arm_scale": [0.25, 0.25, 1.8], "leg_scale": [0.3, 0.3, 1.65]}, "material": {"skin_color": [0.9, 0.8, 0.7, 1.0], "clothing_color": [0.2, 0.6, 0.3, 1.0]}, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "light", "jump_style": "agile"}}]