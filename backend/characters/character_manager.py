"""
角色管理器 - 处理角色类型和配置
"""

import json
from typing import Dict, Any, Optional, List
from pathlib import Path

from backend.utils.file import load_json_file


class CharacterManager:
    """角色管理器"""
    
    def __init__(self):
        """初始化角色管理器"""
        self.character_types = self._load_character_types()
        
    def _load_character_types(self) -> List[Dict[str, Any]]:
        """加载角色类型配置"""
        try:
            return load_json_file("backend/characters/character_types.json")
        except Exception as e:
            print(f"警告: 无法加载角色类型配置: {e}")
            return self._get_default_character_types()
    
    def _get_default_character_types(self) -> List[Dict[str, Any]]:
        """获取默认角色类型"""
        return [
            {
                "name": "human_male",
                "display_name": "男性人类",
                "display_name_en": "Human Male",
                "description": "标准男性人类角色",
                "body_type": "humanoid",
                "height": 1.8,
                "proportions": {
                    "head_scale": 1.0,
                    "torso_scale": [0.8, 0.4, 1.2],
                    "arm_scale": [0.3, 0.3, 1.8],
                    "leg_scale": [0.35, 0.35, 1.6]
                },
                "material": {
                    "skin_color": [0.8, 0.6, 0.4, 1.0],
                    "clothing_color": [0.2, 0.3, 0.8, 1.0]
                },
                "animation_features": {
                    "has_facial_expressions": True,
                    "has_hand_gestures": True,
                    "walking_style": "confident",
                    "jump_style": "athletic"
                }
            }
        ]
    
    def get_character_type(self, character_type: str) -> Optional[Dict[str, Any]]:
        """获取指定的角色类型配置
        
        Args:
            character_type: 角色类型名称
            
        Returns:
            角色类型配置，如果不存在返回None
        """
        for char_type in self.character_types:
            if char_type["name"] == character_type:
                return char_type
        return None
    
    def get_default_character_type(self) -> Dict[str, Any]:
        """获取默认角色类型"""
        return self.get_character_type("human_male") or self.character_types[0]
    
    def get_all_character_types(self) -> List[Dict[str, Any]]:
        """获取所有角色类型"""
        return self.character_types
    
    def get_character_names(self) -> List[str]:
        """获取所有角色类型名称"""
        return [char_type["name"] for char_type in self.character_types]
    
    def validate_character_info(self, character_info: Dict[str, Any]) -> Dict[str, Any]:
        """验证并补全角色信息
        
        Args:
            character_info: 角色信息
            
        Returns:
            验证后的完整角色信息
        """
        if not character_info:
            return {
                "type": "human_male",
                "name": "默认角色",
                "description": "默认人类角色"
            }
        
        # 验证角色类型是否存在
        char_type = character_info.get("type", "human_male")
        if not self.get_character_type(char_type):
            char_type = "human_male"
        
        # 补全缺失的字段
        validated_info = {
            "type": char_type,
            "name": character_info.get("name", "角色"),
            "description": character_info.get("description", "游戏角色")
        }
        
        return validated_info
    
    def get_character_display_name(self, character_type: str, language: str = "zh") -> str:
        """获取角色显示名称
        
        Args:
            character_type: 角色类型
            language: 语言 ('zh' 或 'en')
            
        Returns:
            角色显示名称
        """
        char_config = self.get_character_type(character_type)
        if not char_config:
            return character_type
        
        if language == "en":
            return char_config.get("display_name_en", char_config["name"])
        else:
            return char_config.get("display_name", char_config["name"])
    
    def get_character_proportions(self, character_type: str) -> Dict[str, Any]:
        """获取角色比例配置
        
        Args:
            character_type: 角色类型
            
        Returns:
            角色比例配置
        """
        char_config = self.get_character_type(character_type)
        if not char_config:
            char_config = self.get_default_character_type()
        
        return char_config.get("proportions", {})
    
    def get_character_material(self, character_type: str) -> Dict[str, Any]:
        """获取角色材质配置
        
        Args:
            character_type: 角色类型
            
        Returns:
            角色材质配置
        """
        char_config = self.get_character_type(character_type)
        if not char_config:
            char_config = self.get_default_character_type()
        
        return char_config.get("material", {})
    
    def get_character_animation_features(self, character_type: str) -> Dict[str, Any]:
        """获取角色动画特征
        
        Args:
            character_type: 角色类型
            
        Returns:
            角色动画特征配置
        """
        char_config = self.get_character_type(character_type)
        if not char_config:
            char_config = self.get_default_character_type()
        
        return char_config.get("animation_features", {})
    
    def create_character_summary(self, character_info: Dict[str, Any]) -> str:
        """创建角色摘要信息
        
        Args:
            character_info: 角色信息
            
        Returns:
            角色摘要字符串
        """
        validated_info = self.validate_character_info(character_info)
        char_type = validated_info["type"]
        char_config = self.get_character_type(char_type)
        
        if not char_config:
            return f"角色: {validated_info['name']} (类型: {char_type})"
        
        summary = f"角色: {validated_info['name']}\n"
        summary += f"类型: {char_config['display_name']} ({char_type})\n"
        summary += f"描述: {validated_info['description']}\n"
        summary += f"身高: {char_config['height']}m\n"
        
        features = char_config.get("animation_features", {})
        if features:
            summary += f"动画特征: 行走={features.get('walking_style', 'normal')}, "
            summary += f"跳跃={features.get('jump_style', 'normal')}"
        
        return summary


# 全局角色管理器实例
character_manager = CharacterManager()
