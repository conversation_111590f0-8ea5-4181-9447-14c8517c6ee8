import json
import re
from typing import List, Dict, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, AIMessage

from backend.utils.file import load_json_file


class AgentState(TypedDict):
    """Agent 状态定义"""
    messages: List[Any]
    natural_language_input: str
    parsed_actions: List[Dict[str, Any]]
    character_info: Optional[Dict[str, Any]]
    error: Optional[str]


class GameActionAgent:
    """游戏动作解析 Agent"""

    def __init__(self, model_name: str = "gpt-4o-mini", temperature: float = 0.1):
        """初始化 Agent

        Args:
            model_name: 使用的模型名称
            temperature: 模型温度参数
        """
        self.llm = ChatOpenAI(model=model_name, temperature=temperature)
        self.game_actions = load_json_file("backend/actions/game_action_command_set.json")
        self.parser = JsonOutputParser()

        # 创建 LangGraph
        self.graph = self._create_graph()

    def _create_action_schema_prompt(self) -> str:
        """创建动作模式的提示文本"""
        schema_text = "可用的游戏动作命令:\n\n"

        for action in self.game_actions:
            schema_text += f"**{action['name']} ({action['key']})**\n"
            schema_text += f"描述: {action['desc']}\n"
            schema_text += "参数:\n"

            for param, desc in action['params'].items():
                schema_text += f"  - {param}: {desc}\n"
            schema_text += "\n"

        return schema_text

    def _create_character_schema_prompt(self) -> str:
        """创建角色模式的提示文本"""
        try:
            character_types = load_json_file("backend/characters/character_types.json")
            schema_text = "可用的角色类型:\n\n"

            for char in character_types:
                schema_text += f"**{char['name']}** ({char['display_name']})\n"
                schema_text += f"描述: {char['description']}\n"
                schema_text += f"身高: {char['height']}m, 体型: {char['body_type']}\n"
                schema_text += f"动画特征: 行走风格={char['animation_features']['walking_style']}, "
                schema_text += f"跳跃风格={char['animation_features']['jump_style']}\n\n"

            return schema_text
        except Exception:
            return "角色类型: human_male, human_female, warrior, child, robot, fantasy_elf\n\n"

    def _create_system_prompt(self) -> str:
        """创建系统提示"""
        action_schema = self._create_action_schema_prompt()
        character_schema = self._create_character_schema_prompt()

        return f"""你是一个专业的游戏动作解析器。你的任务是将自然语言描述转换为结构化的游戏动作命令序列，并识别角色信息。

{character_schema}

{action_schema}

**解析规则:**
1. 仔细分析输入的自然语言，识别角色类型和所有动作序列
2. 如果明确提到角色类型（如"战士"、"女性"、"机器人"等），识别对应的角色类型
3. 如果没有明确提到角色，默认使用 "human_male"
4. 将每个动作映射到对应的游戏命令
5. 保持动作的时间顺序和逻辑关系
6. 为每个动作填充合适的参数值
7. 输出格式必须是有效的 JSON 对象，包含角色信息和动作序列

**Output Format:**
```json
{{{{
  "character": {{{{
    "type": "character_type_name",
    "name": "角色名称（如果提到）",
    "description": "角色描述"
  }}}},
  "actions": [
    {{{{
      "action_type": "action_name",
      "action_key": "ACTION_KEY",
      "parameters": {{{{
        "param_name": "param_value"
      }}}},
      "sequence_number": number,
      "description_en": "English description",
      "description_zh": "中文描述"
    }}}}
  ]
}}}}
```

**Example 1:**
Input: "战士挥手3秒，然后向前五步走，并跳跃"
Output:
```json
{{{{
  "character": {{{{
    "type": "warrior",
    "name": "战士",
    "description": "强壮的战士角色"
  }}}},
  "actions": [
    {{{{
      "action_type": "emotion",
      "action_key": "EMOTION",
      "parameters": {{{{
        "emotion": "happy",
        "intensity": 0.7
      }}}},
      "sequence_number": 1,
      "description_en": "Express happy emotion by waving",
      "description_zh": "挥手表达快乐情绪"
    }}}},
    {{{{
      "action_type": "idle",
      "action_key": "IDLE",
      "parameters": {{{{
        "mood": "alert",
        "duration": 3.0
      }}}},
      "sequence_number": 2,
      "description_en": "Maintain waving gesture for 3 seconds",
      "description_zh": "保持挥手姿势3秒"
    }}}},
    {{{{
      "action_type": "move",
      "action_key": "MOVE",
      "parameters": {{{{
        "direction": "forward",
        "speed": 0.5,
        "duration": 5.0
      }}}},
      "sequence_number": 3,
      "description_en": "Move forward for five steps",
      "description_zh": "向前走五步"
    }}}},
    {{{{
      "action_type": "jump",
      "action_key": "JUMP",
      "parameters": {{{{
        "height": 1.0,
        "distance": 1.0,
        "style": "normal"
      }}}},
      "sequence_number": 4,
      "description_en": "Perform a jump action",
      "description_zh": "执行跳跃动作"
    }}}}
  ]
}}}}
```

**Example 2:**
Input: "小女孩蹦蹦跳跳地向前跑"
Output:
```json
{{{{
  "character": {{{{
    "type": "child",
    "name": "小女孩",
    "description": "活泼的儿童角色"
  }}}},
  "actions": [
    {{{{
      "action_type": "move",
      "action_key": "MOVE",
      "parameters": {{{{
        "direction": "forward",
        "speed": 0.8,
        "duration": 3.0
      }}}},
      "sequence_number": 1,
      "description_en": "Run forward with bouncy style",
      "description_zh": "蹦蹦跳跳地向前跑"
    }}}}
  ]
}}}}
```

请严格按照上述格式输出，确保 JSON 格式正确且包含所有必要字段。"""

    def _create_graph(self) -> StateGraph:
        """创建 LangGraph 工作流"""
        workflow = StateGraph(AgentState)

        # 添加节点
        workflow.add_node("parse_input", self._parse_input_node)
        workflow.add_node("validate_output", self._validate_output_node)
        workflow.add_node("error_handler", self._error_handler_node)

        # 设置入口点
        workflow.set_entry_point("parse_input")

        # 添加边
        workflow.add_conditional_edges(
            "parse_input",
            self._should_validate,
            {
                "validate": "validate_output",
                "error": "error_handler"
            }
        )

        workflow.add_edge("validate_output", END)
        workflow.add_edge("error_handler", END)

        return workflow.compile()

    def _parse_input_node(self, state: AgentState) -> AgentState:
        """解析输入节点"""
        try:
            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([
                ("system", self._create_system_prompt()),
                ("human", "请解析以下自然语言描述为游戏动作命令:\n\n{input}")
            ])

            # 创建链
            chain = prompt | self.llm | self.parser

            # 执行解析
            result = chain.invoke({"input": state["natural_language_input"]})

            # 处理新的输出格式
            if isinstance(result, dict) and "character" in result and "actions" in result:
                state["character_info"] = result["character"]
                state["parsed_actions"] = result["actions"]
                char_name = result["character"].get("name", result["character"].get("type", "角色"))
                state["messages"].append(AIMessage(content=f"成功识别角色 '{char_name}' 和 {len(state['parsed_actions'])} 个动作"))
            else:
                # 兼容旧格式
                state["parsed_actions"] = result if isinstance(result, list) else [result]
                state["character_info"] = {"type": "human_male", "name": "默认角色", "description": "默认人类角色"}
                state["messages"].append(AIMessage(content=f"成功解析了 {len(state['parsed_actions'])} 个动作（使用默认角色）"))

        except Exception as e:
            state["error"] = f"解析失败: {str(e)}"
            state["messages"].append(AIMessage(content=f"解析出错: {str(e)}"))

        return state

    def _validate_output_node(self, state: AgentState) -> AgentState:
        """验证输出节点"""
        try:
            actions = state["parsed_actions"]

            # 验证每个动作的格式
            for i, action in enumerate(actions):
                if not self._validate_action_format(action):
                    raise ValueError(f"动作 {i+1} 格式不正确: {action}")

            state["messages"].append(AIMessage(content="动作序列验证通过"))

        except Exception as e:
            state["error"] = f"验证失败: {str(e)}"
            state["messages"].append(AIMessage(content=f"验证出错: {str(e)}"))

        return state

    def _error_handler_node(self, state: AgentState) -> AgentState:
        """错误处理节点"""
        error_msg = state.get("error", "未知错误")
        state["messages"].append(AIMessage(content=f"处理完成，但存在错误: {error_msg}"))
        state["parsed_actions"] = []
        return state

    def _should_validate(self, state: AgentState) -> str:
        """条件判断：是否需要验证"""
        if state.get("error"):
            return "error"
        return "validate"

    def _validate_action_format(self, action: Dict[str, Any]) -> bool:
        """验证单个动作的格式"""
        required_fields = ["action_type", "action_key", "parameters", "sequence_number", "description_en"]

        # 检查必需字段
        for field in required_fields:
            if field not in action:
                return False

        # 检查动作是否在支持列表中
        action_names = [a["name"] for a in self.game_actions]
        if action["action_type"] not in action_names:
            return False

        return True

    def parse_natural_language(self, natural_language: str) -> Dict[str, Any]:
        """解析自然语言为游戏动作命令

        Args:
            natural_language: 自然语言描述

        Returns:
            包含解析结果的字典
        """
        # 初始化状态
        initial_state = AgentState(
            messages=[HumanMessage(content=natural_language)],
            natural_language_input=natural_language,
            parsed_actions=[],
            character_info=None,
            error=None
        )

        # 执行图
        result = self.graph.invoke(initial_state)

        # 返回结果
        return {
            "success": not bool(result.get("error")),
            "actions": result.get("parsed_actions", []),
            "character": result.get("character_info"),
            "error": result.get("error"),
            "input": natural_language
        }

    async def aparse_natural_language(self, natural_language: str) -> Dict[str, Any]:
        """异步解析自然语言为游戏动作命令

        Args:
            natural_language: 自然语言描述

        Returns:
            包含解析结果的字典
        """
        # 初始化状态
        initial_state = AgentState(
            messages=[HumanMessage(content=natural_language)],
            natural_language_input=natural_language,
            parsed_actions=[],
            character_info=None,
            error=None
        )

        # 异步执行图
        result = await self.graph.ainvoke(initial_state)

        # 返回结果
        return {
            "success": not bool(result.get("error")),
            "actions": result.get("parsed_actions", []),
            "character": result.get("character_info"),
            "error": result.get("error"),
            "input": natural_language
        }

    def get_supported_actions(self) -> List[Dict[str, Any]]:
        """获取支持的动作列表"""
        return self.game_actions

    def validate_action_sequence(self, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证动作序列的有效性

        Args:
            actions: 动作序列列表

        Returns:
            验证结果
        """
        errors = []
        warnings = []

        for i, action in enumerate(actions):
            # 验证格式
            if not self._validate_action_format(action):
                errors.append(f"动作 {i+1} 格式不正确")
                continue

            # 验证参数
            action_def = next((a for a in self.game_actions if a["name"] == action["action"]), None)
            if action_def:
                param_errors = self._validate_action_params(action["params"], action_def["params"])
                if param_errors:
                    errors.extend([f"动作 {i+1} 参数错误: {err}" for err in param_errors])

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }

    def _validate_action_params(self, params: Dict[str, Any], param_schema: Dict[str, str]) -> List[str]:
        """验证动作参数"""
        errors = []

        for param_name, param_value in params.items():
            if param_name not in param_schema:
                errors.append(f"未知参数: {param_name}")
                continue

            # 这里可以添加更详细的参数验证逻辑
            # 例如检查数值范围、枚举值等

        return errors