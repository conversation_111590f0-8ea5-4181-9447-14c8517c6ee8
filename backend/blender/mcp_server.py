"""
MCP Server Integration Module for Blender
Manages the Model Context Protocol server for enhanced Blender operations
"""

import asyncio
import json
import socket
import subprocess
import tempfile
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from contextlib import asynccontextmanager

from loguru import logger
import requests


@dataclass
class MCPConnection:
    """Manages connection to the MCP server"""
    host: str = "localhost"
    port: int = 9876
    sock: Optional[socket.socket] = None
    connected: bool = False
    
    def connect(self) -> bool:
        """Connect to the MCP server"""
        if self.connected and self.sock:
            return True
            
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            self.connected = True
            logger.info(f"Connected to MCP server at {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {str(e)}")
            self.sock = None
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from the MCP server"""
        if self.sock:
            try:
                self.sock.close()
            except Exception as e:
                logger.error(f"Error disconnecting from MCP server: {str(e)}")
            finally:
                self.sock = None
                self.connected = False
    
    def send_command(self, command_type: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a command to the MCP server and return the response"""
        if not self.connected and not self.connect():
            raise ConnectionError("Not connected to MCP server")
        
        command = {
            "type": command_type,
            "params": params or {}
        }
        
        try:
            # Send the command
            self.sock.sendall(json.dumps(command).encode('utf-8'))
            
            # Set timeout for receiving
            self.sock.settimeout(15.0)
            
            # Receive the response
            response_data = self._receive_full_response()
            response = json.loads(response_data.decode('utf-8'))
            
            if response.get("status") == "error":
                logger.error(f"MCP server error: {response.get('message')}")
                raise Exception(response.get("message", "Unknown error from MCP server"))
            
            return response.get("result", {})
            
        except socket.timeout:
            logger.error("Socket timeout while waiting for response from MCP server")
            self.sock = None
            self.connected = False
            raise Exception("Timeout waiting for MCP server response")
        except Exception as e:
            logger.error(f"Error communicating with MCP server: {str(e)}")
            self.sock = None
            self.connected = False
            raise Exception(f"Communication error with MCP server: {str(e)}")
    
    def _receive_full_response(self, buffer_size=8192):
        """Receive the complete response, potentially in multiple chunks"""
        chunks = []
        self.sock.settimeout(15.0)
        
        try:
            while True:
                try:
                    chunk = self.sock.recv(buffer_size)
                    if not chunk:
                        if not chunks:
                            raise Exception("Connection closed before receiving any data")
                        break
                    chunks.append(chunk)
                    
                    # Check if we've received a complete JSON object
                    try:
                        data = b''.join(chunks)
                        json.loads(data.decode('utf-8'))
                        return data
                    except json.JSONDecodeError:
                        continue
                        
                except socket.timeout:
                    break
                    
            if chunks:
                data = b''.join(chunks)
                try:
                    json.loads(data.decode('utf-8'))
                    return data
                except json.JSONDecodeError:
                    raise Exception("Incomplete JSON response received")
            else:
                raise Exception("No data received")
                
        except Exception as e:
            logger.error(f"Error during receive: {str(e)}")
            raise


class MCPServerManager:
    """Manages the MCP server lifecycle and operations"""
    
    def __init__(self):
        self.connection = MCPConnection()
        self.server_process: Optional[subprocess.Popen] = None
        self.addon_installed = False
        
    def start_server(self) -> bool:
        """Start the MCP server if not already running"""
        try:
            # Check if server is already running
            if self.connection.connect():
                logger.info("MCP server is already running")
                return True
            
            # Start the server process
            logger.info("Starting MCP server...")
            self.server_process = subprocess.Popen(
                ["uvx", "blender-mcp"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a moment for the server to start
            time.sleep(2)
            
            # Try to connect
            max_retries = 10
            for i in range(max_retries):
                if self.connection.connect():
                    logger.info("MCP server started successfully")
                    return True
                time.sleep(1)
            
            logger.error("Failed to connect to MCP server after starting")
            return False
            
        except Exception as e:
            logger.error(f"Failed to start MCP server: {str(e)}")
            return False
    
    def stop_server(self):
        """Stop the MCP server"""
        try:
            self.connection.disconnect()
            
            if self.server_process:
                self.server_process.terminate()
                try:
                    self.server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.server_process.kill()
                    self.server_process.wait()
                self.server_process = None
                
            logger.info("MCP server stopped")
            
        except Exception as e:
            logger.error(f"Error stopping MCP server: {str(e)}")
    
    def is_server_running(self) -> bool:
        """Check if the MCP server is running"""
        try:
            return self.connection.connect()
        except:
            return False
    
    def get_scene_info(self) -> Dict[str, Any]:
        """Get detailed information about the current Blender scene"""
        try:
            result = self.connection.send_command("get_scene_info")
            return json.loads(result) if isinstance(result, str) else result
        except Exception as e:
            logger.error(f"Error getting scene info: {str(e)}")
            return {"error": str(e)}
    
    def get_object_info(self, object_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific object"""
        try:
            result = self.connection.send_command("get_object_info", {"name": object_name})
            return json.loads(result) if isinstance(result, str) else result
        except Exception as e:
            logger.error(f"Error getting object info: {str(e)}")
            return {"error": str(e)}
    
    def execute_blender_code(self, code: str) -> Dict[str, Any]:
        """Execute arbitrary Python code in Blender"""
        try:
            result = self.connection.send_command("execute_code", {"code": code})
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error executing Blender code: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def get_viewport_screenshot(self, max_size: int = 800) -> Optional[bytes]:
        """Capture a screenshot of the current Blender 3D viewport"""
        try:
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
                temp_path = tmp_file.name
            
            result = self.connection.send_command("get_viewport_screenshot", {
                "max_size": max_size,
                "filepath": temp_path,
                "format": "png"
            })
            
            if "error" in result:
                raise Exception(result["error"])
            
            # Read the screenshot file
            with open(temp_path, 'rb') as f:
                image_data = f.read()
            
            # Clean up temp file
            Path(temp_path).unlink(missing_ok=True)
            
            return image_data
            
        except Exception as e:
            logger.error(f"Error capturing screenshot: {str(e)}")
            return None
    
    def install_addon(self, addon_path: str) -> bool:
        """Install the Blender MCP addon"""
        try:
            # This would typically involve copying the addon file to Blender's addon directory
            # and enabling it through Blender's API
            install_code = f"""
import bpy
import addon_utils

# Install addon
bpy.ops.preferences.addon_install(filepath="{addon_path}")

# Enable addon
addon_utils.enable("blender_mcp", default_set=True)

# Save preferences
bpy.ops.wm.save_userpref()
"""
            
            result = self.execute_blender_code(install_code)
            if result.get("success"):
                self.addon_installed = True
                logger.info("Blender MCP addon installed successfully")
                return True
            else:
                logger.error(f"Failed to install addon: {result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"Error installing addon: {str(e)}")
            return False
    
    def check_addon_status(self) -> Dict[str, Any]:
        """Check if the Blender MCP addon is installed and enabled"""
        try:
            check_code = """
import bpy
import addon_utils

addon_name = "blender_mcp"
addon_info = addon_utils.check(addon_name)
is_enabled = addon_utils.check(addon_name)[1]

result = {
    "installed": addon_info[0] is not None,
    "enabled": is_enabled,
    "addon_name": addon_name
}

print(f"Addon status: {result}")
"""
            
            result = self.execute_blender_code(check_code)
            if result.get("success"):
                # Parse the result from the printed output
                output = result.get("result", "")
                if "Addon status:" in output:
                    # Extract the status dict from the output
                    import ast
                    status_line = output.split("Addon status:")[1].strip()
                    try:
                        status = ast.literal_eval(status_line)
                        return status
                    except:
                        pass
            
            return {"installed": False, "enabled": False, "error": "Could not determine addon status"}
            
        except Exception as e:
            logger.error(f"Error checking addon status: {str(e)}")
            return {"installed": False, "enabled": False, "error": str(e)}


# Global MCP server manager instance
_mcp_manager: Optional[MCPServerManager] = None


def get_mcp_manager() -> MCPServerManager:
    """Get or create the global MCP server manager"""
    global _mcp_manager
    if _mcp_manager is None:
        _mcp_manager = MCPServerManager()
    return _mcp_manager


def cleanup_mcp_manager():
    """Clean up the global MCP server manager"""
    global _mcp_manager
    if _mcp_manager:
        _mcp_manager.stop_server()
        _mcp_manager = None
