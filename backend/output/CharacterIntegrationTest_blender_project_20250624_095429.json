{"project_name": "CharacterIntegrationTest", "scene": {"scene_name": "CharacterIntegrationTest_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 68, "objects": []}, "animation_clips": [{"name": "CharacterIntegrationTest_Animation", "start_frame": 1, "end_frame": 68, "frame_rate": 24.0, "channels": [], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "character_integration_test.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent_MCP_Enhanced", "actions_count": 2, "total_frames": 68, "mcp_enhanced": true, "scene_objects": 3, "character": {"type": "warrior", "name": "强壮的战士", "description": "强壮的战士角色"}, "character_config": {"type": "warrior", "display_name": "战士", "height": 1.9, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "heavy", "jump_style": "powerful"}}}}