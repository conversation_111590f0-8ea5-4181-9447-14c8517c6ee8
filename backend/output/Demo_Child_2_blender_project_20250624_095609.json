{"project_name": "Demo_Child_2", "scene": {"scene_name": "Demo_Child_2_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 169, "objects": []}, "animation_clips": [{"name": "Demo_Child_2_Animation", "start_frame": 1, "end_frame": 169, "frame_rate": 24.0, "channels": [], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "demo_child_2.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent_MCP_Enhanced", "actions_count": 3, "total_frames": 169, "mcp_enhanced": true, "scene_objects": 3, "character": {"type": "child", "name": "小女孩", "description": "活泼的儿童角色"}, "character_config": {"type": "child", "display_name": "儿童", "height": 1.2, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "bouncy", "jump_style": "playful"}}}}