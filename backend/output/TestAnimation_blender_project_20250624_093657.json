{"project_name": "TestAnimation", "scene": {"scene_name": "TestAnimation_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 73, "objects": []}, "animation_clips": [{"name": "TestAnimation_Animation", "start_frame": 1, "end_frame": 73, "frame_rate": 24.0, "channels": [{"target_object": "TestCharacter", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 1, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 49, "value": [0.0, 5.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "TestCharacter", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 49, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 58, "value": [0.0, 0.0, 1.5], "interpolation": "EASE_OUT", "handle_left": null, "handle_right": null}, {"frame": 73, "value": [0.0, 0.0, 0.0], "interpolation": "EASE_IN", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "TestCharacter", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 49, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 73, "value": [0.0, 1.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "test_animation.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 2, "total_frames": 73}}