#!/usr/bin/env python3
"""
测试角色集成功能
"""

import json
from backend.agent.animation_agent import GameActionAgent
from backend.blender.fbx_exporter import FBXExporter
from backend.characters.character_manager import character_manager


def test_character_parsing():
    """测试角色解析功能"""
    print("=== 测试角色解析功能 ===")
    
    agent = GameActionAgent()
    
    # 测试用例
    test_cases = [
        "战士向前冲锋并攻击",
        "小女孩蹦蹦跳跳地向前跑",
        "机器人缓慢地向左移动",
        "精灵优雅地跳跃",
        "人物挥手3秒然后向前走"  # 没有明确角色类型
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_input} ---")
        
        result = agent.parse_natural_language(test_input)
        
        if result["success"]:
            character = result.get("character")
            actions = result.get("actions", [])
            
            print(f"解析成功!")
            print(f"角色信息: {character}")
            print(f"动作数量: {len(actions)}")
            
            for j, action in enumerate(actions, 1):
                print(f"  动作 {j}: {action.get('action_type')} - {action.get('description_zh', 'N/A')}")
        else:
            print(f"解析失败: {result.get('error')}")


def test_character_manager():
    """测试角色管理器"""
    print("\n=== 测试角色管理器 ===")
    
    # 获取所有角色类型
    character_types = character_manager.get_all_character_types()
    print(f"可用角色类型数量: {len(character_types)}")
    
    for char_type in character_types:
        print(f"- {char_type['name']}: {char_type['display_name']} (身高: {char_type['height']}m)")
    
    # 测试角色配置获取
    print("\n--- 测试角色配置 ---")
    test_character_types = ["warrior", "child", "robot", "nonexistent"]
    
    for char_type in test_character_types:
        config = character_manager.get_character_type(char_type)
        if config:
            print(f"{char_type}: 找到配置 - {config['display_name']}")
            print(f"  比例: {config['proportions']}")
            print(f"  材质: {config['material']}")
        else:
            print(f"{char_type}: 未找到配置")


def test_full_pipeline():
    """测试完整流程"""
    print("\n=== 测试完整流程 ===")
    
    # 创建代理和导出器
    agent = GameActionAgent()
    exporter = FBXExporter()
    
    # 测试输入
    test_input = "强壮的战士挥舞大剑攻击，然后向前冲锋"
    
    print(f"输入: {test_input}")
    
    # 1. 解析自然语言
    parse_result = agent.parse_natural_language(test_input)
    
    if not parse_result["success"]:
        print(f"解析失败: {parse_result.get('error')}")
        return
    
    character_info = parse_result.get("character")
    actions = parse_result.get("actions", [])
    
    print(f"解析成功!")
    print(f"角色: {character_info}")
    print(f"动作数量: {len(actions)}")
    
    # 2. 创建角色摘要
    character_summary = character_manager.create_character_summary(character_info)
    print(f"\n角色摘要:\n{character_summary}")
    
    # 3. 导出FBX
    print(f"\n开始导出FBX...")
    
    export_result = exporter.export_actions_to_fbx(
        actions=actions,
        output_path="character_integration_test.fbx",
        project_name="CharacterIntegrationTest",
        character_name=character_info.get("name", "TestCharacter"),
        character_info=character_info
    )
    
    if export_result["success"]:
        print(f"FBX导出成功!")
        print(f"输出路径: {export_result['output_path']}")
        print(f"文件大小: {export_result['file_size']} 字节")
        
        # 检查Blender项目数据中的角色信息
        blender_project = export_result.get("blender_project", {})
        metadata = blender_project.get("metadata", {})
        
        if "character" in metadata:
            print(f"Blender项目中的角色信息: {metadata['character']}")
        
        if "character_config" in metadata:
            print(f"Blender项目中的角色配置: {metadata['character_config']}")
    else:
        print(f"FBX导出失败: {export_result.get('error')}")


def test_different_characters():
    """测试不同角色类型"""
    print("\n=== 测试不同角色类型 ===")
    
    agent = GameActionAgent()
    exporter = FBXExporter()
    
    # 不同角色的测试用例
    test_cases = [
        ("小孩蹦跳", "child"),
        ("机器人行走", "robot"),
        ("精灵跳跃", "fantasy_elf"),
        ("女性挥手", "human_female")
    ]
    
    for description, expected_type in test_cases:
        print(f"\n--- 测试: {description} (期望类型: {expected_type}) ---")
        
        # 解析
        parse_result = agent.parse_natural_language(description)
        
        if parse_result["success"]:
            character_info = parse_result.get("character")
            actions = parse_result.get("actions", [])
            
            actual_type = character_info.get("type", "unknown")
            print(f"实际类型: {actual_type}")
            print(f"匹配: {'✓' if actual_type == expected_type else '✗'}")
            
            # 快速导出测试（不实际生成文件）
            try:
                from backend.blender.converter import GameActionToBlenderConverter
                converter = GameActionToBlenderConverter()
                blender_project = converter.convert_actions_to_blender(
                    actions, f"Test_{expected_type}", character_info
                )
                
                metadata = blender_project.metadata
                if "character_config" in metadata:
                    config = metadata["character_config"]
                    print(f"角色配置: {config['display_name']} (身高: {config['height']}m)")
                else:
                    print("未找到角色配置")
                    
            except Exception as e:
                print(f"转换测试失败: {e}")
        else:
            print(f"解析失败: {parse_result.get('error')}")


def main():
    """主函数"""
    print("Motion Agent 角色集成测试")
    print("=" * 50)
    
    try:
        # 运行所有测试
        test_character_parsing()
        test_character_manager()
        test_full_pipeline()
        test_different_characters()
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
