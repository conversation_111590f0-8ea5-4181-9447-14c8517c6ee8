'use client';

import { useState, useEffect } from 'react';
import AnimationPreview from '@/components/animation-preview';

interface FBXFile {
  name: string;
  path: string;
  size: number;
  modified: string;
  download_url: string;
}

export default function Home() {
  const [selectedFBXFile, setSelectedFBXFile] = useState<string>('');
  const [fbxFiles, setFbxFiles] = useState<FBXFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取 FBX 文件列表
  const fetchFBXFiles = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('http://localhost:8000/output-files?file_type=fbx');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setFbxFiles(data.files || []);

      // 如果有文件且没有选中的文件，自动选择第一个
      if (data.files && data.files.length > 0 && !selectedFBXFile) {
        setSelectedFBXFile(data.files[0].download_url);
      }
    } catch (err) {
      console.error('Error fetching FBX files:', err);
      setError('Failed to load FBX files. Make sure the backend is running.');
      setFbxFiles([]);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取文件列表
  useEffect(() => {
    fetchFBXFiles();
  }, []);

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Motion Agent - FBX Animation Preview
          </h1>
          <p className="text-gray-600">
            Preview and interact with 3D FBX animations generated by the backend
          </p>
        </header>

        <main className="space-y-8">
          {/* FBX File Selector */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Select FBX File
              </h2>
              <button
                onClick={fetchFBXFiles}
                disabled={loading}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Loading...' : 'Refresh'}
              </button>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="fbx-select" className="block text-sm font-medium text-gray-700 mb-2">
                Choose an FBX file to preview:
              </label>
              <select
                id="fbx-select"
                value={selectedFBXFile}
                onChange={(e) => setSelectedFBXFile(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={loading || fbxFiles.length === 0}
              >
                <option value="">
                  {loading ? 'Loading files...' : fbxFiles.length === 0 ? 'No FBX files found' : 'Select an FBX file'}
                </option>
                {fbxFiles.map((file) => (
                  <option key={file.path} value={file.download_url}>
                    {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                  </option>
                ))}
              </select>
            </div>

            {fbxFiles.length > 0 && (
              <div className="text-sm text-gray-600">
                Found {fbxFiles.length} FBX file{fbxFiles.length !== 1 ? 's' : ''} in the backend output directory.
              </div>
            )}
          </div>

          {/* Animation Preview */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Animation Preview
            </h2>

            {selectedFBXFile ? (
              <AnimationPreview
                fbxUrl={selectedFBXFile}
                animationName={fbxFiles.find(f => f.download_url === selectedFBXFile)?.name || "Selected Animation"}
                autoPlay={true}
                showGrid={true}
                showStats={false}
                cameraPosition={[5, 5, 5]}
                backgroundColor="#f8fafc"
              />
            ) : (
              <div className="h-96 flex items-center justify-center bg-gray-100 rounded-lg">
                <div className="text-center text-gray-500">
                  <p className="text-lg mb-2">No FBX file selected</p>
                  <p className="text-sm">Please select an FBX file from the dropdown above to preview the animation.</p>
                </div>
              </div>
            )}
          </div>

          {/* Controls and Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                3D Viewer Controls
              </h3>
              <ul className="text-gray-600 space-y-1">
                <li>• Left click + drag: Rotate camera</li>
                <li>• Right click + drag: Pan camera</li>
                <li>• Scroll wheel: Zoom in/out</li>
                <li>• Animation plays automatically</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                File Information
              </h3>
              {selectedFBXFile && fbxFiles.length > 0 ? (
                <div className="text-gray-600 space-y-1">
                  {(() => {
                    const selectedFile = fbxFiles.find(f => f.download_url === selectedFBXFile);
                    return selectedFile ? (
                      <>
                        <li>• File: {selectedFile.name}</li>
                        <li>• Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</li>
                        <li>• Modified: {new Date(selectedFile.modified).toLocaleString()}</li>
                        <li>•
                          <a
                            href={selectedFile.download_url}
                            download={selectedFile.name}
                            className="text-blue-500 hover:text-blue-700 underline"
                          >
                            Download File
                          </a>
                        </li>
                      </>
                    ) : null;
                  })()}
                </div>
              ) : (
                <p className="text-gray-500">Select a file to view information</p>
              )}
            </div>
          </div>

          {/* API Information */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">
              API Integration
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Available Endpoints</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• POST /generate-actions - Generate actions from text</li>
                  <li>• POST /export-fbx - Export FBX animation</li>
                  <li>• GET /health - System health check</li>
                  <li>• GET /output-files - List generated files</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Backend Status</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Backend URL: http://localhost:8000</li>
                  <li>• Files loaded: {fbxFiles.length}</li>
                  <li>• Status: {error ? 'Disconnected' : 'Connected'}</li>
                </ul>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
