'use client';

import { useState } from 'react';
import AnimationPreview from '@/components/animation-preview';
import MCPEnhancedPreview from '@/components/mcp-enhanced-preview';
import MCPControlPanel from '@/components/mcp-control-panel';

export default function Home() {
  const [mcpEnabled, setMcpEnabled] = useState(true);
  const [mcpStatus, setMcpStatus] = useState<any>(null);

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Motion Agent - MCP Enhanced Animation Preview
          </h1>
          <p className="text-gray-600">
            Preview and interact with 3D FBX animations using React Three Fiber with MCP integration
          </p>

          <div className="mt-4 flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={mcpEnabled}
                onChange={(e) => setMcpEnabled(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Enable MCP Features</span>
            </label>

            {mcpStatus && (
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  mcpStatus.server_running ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm text-gray-600">
                  MCP: {mcpStatus.server_running ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            )}
          </div>
        </header>

        <main className="space-y-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Samba Dancing Animation
            </h2>
            <AnimationPreview
              fbxUrl="/models/SambaDancing.fbx"
              animationName="Samba Dance"
              autoPlay={true}
              showGrid={true}
              showStats={false}
              cameraPosition={[5, 5, 5]}
              backgroundColor="#f8fafc"
            />
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">
              Controls
            </h3>
            <ul className="text-gray-600 space-y-1">
              <li>• Left click + drag: Rotate camera</li>
              <li>• Right click + drag: Pan camera</li>
              <li>• Scroll wheel: Zoom in/out</li>
              <li>• Animation plays automatically</li>
            </ul>
          </div>
        </main>
      </div>
    </div>
  );
}
