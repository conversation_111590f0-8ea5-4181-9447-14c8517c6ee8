'use client';

import { useState, useEffect } from 'react';
import AnimationPreview from '@/components/animation-preview';

interface FBXFile {
  name: string;
  path: string;
  size: number;
  modified: string;
  download_url: string;
}

export default function HistoryPage() {
  const [selectedFBXFile, setSelectedFBXFile] = useState<string>('');
  const [fbxFiles, setFbxFiles] = useState<FBXFile[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<FBXFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'modified'>('modified');

  const API_BASE_URL = 'http://localhost:8000';

  // 获取 FBX 文件列表
  const fetchFBXFiles = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/output-files?file_type=fbx`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // 为每个文件添加完整的 URL
      const filesWithFullUrls = (data.files || []).map((file: FBXFile) => ({
        ...file,
        download_url: `${API_BASE_URL}${file.download_url}`
      }));

      setFbxFiles(filesWithFullUrls);

      // 如果有文件且没有选中的文件，自动选择第一个
      if (filesWithFullUrls.length > 0 && !selectedFBXFile) {
        setSelectedFBXFile(filesWithFullUrls[0].download_url);
      }
    } catch (err) {
      console.error('Error fetching FBX files:', err);
      setError('加载FBX文件失败，请确保后端服务正在运行。');
      setFbxFiles([]);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取文件列表
  useEffect(() => {
    fetchFBXFiles();
  }, []);

  const selectedFile = fbxFiles.find(f => f.download_url === selectedFBXFile);

  return (
    <div className="space-y-6">
      {/* File Selector Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            历史文件列表
          </h2>
          <button
            onClick={fetchFBXFiles}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                加载中...
              </>
            ) : (
              '刷新列表'
            )}
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="mb-4">
          <label htmlFor="fbx-select" className="block text-sm font-medium text-gray-700 mb-2">
            选择要预览的FBX文件：
          </label>
          <select
            id="fbx-select"
            value={selectedFBXFile}
            onChange={(e) => setSelectedFBXFile(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={loading || fbxFiles.length === 0}
          >
            <option value="">
              {loading ? '加载文件中...' : fbxFiles.length === 0 ? '未找到FBX文件' : '请选择一个FBX文件'}
            </option>
            {fbxFiles.map((file) => (
              <option key={file.path} value={file.download_url}>
                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB) - {new Date(file.modified).toLocaleDateString()}
              </option>
            ))}
          </select>
        </div>

        {fbxFiles.length > 0 && (
          <div className="text-sm text-gray-600">
            共找到 {fbxFiles.length} 个FBX文件
          </div>
        )}
      </div>

      {/* Animation Preview Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          动画预览
        </h2>

        {selectedFBXFile ? (
          <AnimationPreview
            fbxUrl={selectedFBXFile}
            animationName={selectedFile?.name || "Selected Animation"}
            autoPlay={true}
            showGrid={true}
            showStats={false}
            cameraPosition={[5, 5, 5]}
            backgroundColor="#f8fafc"
          />
        ) : (
          <div className="h-96 flex items-center justify-center bg-gray-100 rounded-lg">
            <div className="text-center text-gray-500">
              <p className="text-lg mb-2">未选择FBX文件</p>
              <p className="text-sm">请从上方下拉菜单中选择一个FBX文件来预览动画</p>
            </div>
          </div>
        )}
      </div>

      {/* File Information Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">
            文件信息
          </h3>
          {selectedFile ? (
            <div className="text-gray-600 space-y-2">
              <p><strong>文件名：</strong> {selectedFile.name}</p>
              <p><strong>文件大小：</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
              <p><strong>修改时间：</strong> {new Date(selectedFile.modified).toLocaleString()}</p>
              <p><strong>文件路径：</strong> {selectedFile.path}</p>
              <div className="mt-3">
                <a
                  href={selectedFile.download_url}
                  download={selectedFile.name}
                  className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  下载文件
                </a>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">请选择文件以查看详细信息</p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">
            3D查看器控制
          </h3>
          <ul className="text-gray-600 space-y-1">
            <li>• 左键拖拽：旋转相机</li>
            <li>• 右键拖拽：平移相机</li>
            <li>• 滚轮：缩放视图</li>
            <li>• 动画自动播放</li>
          </ul>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">
          文件统计
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-blue-700">
          <div className="text-center">
            <div className="text-2xl font-bold">{fbxFiles.length}</div>
            <div className="text-sm">总文件数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {fbxFiles.length > 0 ? (fbxFiles.reduce((sum, file) => sum + file.size, 0) / 1024 / 1024).toFixed(1) : '0'}
            </div>
            <div className="text-sm">总大小 (MB)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {fbxFiles.length > 0 ? (fbxFiles.reduce((sum, file) => sum + file.size, 0) / fbxFiles.length / 1024 / 1024).toFixed(1) : '0'}
            </div>
            <div className="text-sm">平均大小 (MB)</div>
          </div>
        </div>
      </div>
    </div>
  );
}
