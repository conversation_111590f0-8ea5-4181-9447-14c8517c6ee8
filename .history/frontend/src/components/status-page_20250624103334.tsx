'use client';

import { useState, useEffect } from 'react';

interface HealthStatus {
  status: string;
  components: {
    action_parser: boolean;
    game_agent: boolean;
    fbx_exporter: boolean;
    addon_manager: boolean;
    blender: boolean;
    mcp_enabled: boolean;
    mcp_server_running: boolean;
  };
  blender_info: {
    available: boolean;
    version?: string;
    path?: string;
    platform?: string;
  };
  mcp_status: {
    enabled: boolean;
    server_running: boolean;
    server_url?: string;
  };
}

interface SystemInfo {
  message: string;
  version: string;
  status: string;
  llm_available: boolean;
}

export default function StatusPage() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const API_BASE_URL = 'http://localhost:8000';

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 获取健康状态
      const healthResponse = await fetch(`${API_BASE_URL}/health`);
      if (!healthResponse.ok) {
        throw new Error(`健康检查失败: ${healthResponse.status}`);
      }
      const healthData = await healthResponse.json();
      setHealthStatus(healthData);

      // 获取系统信息
      const systemResponse = await fetch(`${API_BASE_URL}/`);
      if (!systemResponse.ok) {
        throw new Error(`系统信息获取失败: ${systemResponse.status}`);
      }
      const systemData = await systemResponse.json();
      setSystemInfo(systemData);

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching status:', err);
      setError(err instanceof Error ? err.message : '获取状态信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    // 每30秒自动刷新一次
    const interval = setInterval(fetchStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: boolean) => {
    return status ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (status: boolean) => {
    return status ? '✅' : '❌';
  };

  const getStatusText = (status: boolean) => {
    return status ? '正常' : '异常';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            系统状态监控
          </h2>
          <button
            onClick={fetchStatus}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                刷新中...
              </>
            ) : (
              '刷新状态'
            )}
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {lastUpdated && (
          <div className="text-sm text-gray-500">
            最后更新: {lastUpdated.toLocaleString()}
          </div>
        )}
      </div>

      {/* System Overview */}
      {systemInfo && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">系统概览</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{systemInfo.version}</div>
              <div className="text-sm text-gray-600">版本号</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{systemInfo.status}</div>
              <div className="text-sm text-gray-600">运行状态</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {getStatusIcon(systemInfo.llm_available)}
              </div>
              <div className="text-sm text-gray-600">LLM可用性</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {healthStatus?.status === 'healthy' ? '✅' : '❌'}
              </div>
              <div className="text-sm text-gray-600">整体健康</div>
            </div>
          </div>
        </div>
      )}

      {/* Component Status */}
      {healthStatus && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">组件状态</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(healthStatus.components).map(([key, status]) => (
              <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center">
                  <span className="mr-3 text-lg">{getStatusIcon(status)}</span>
                  <span className="font-medium">
                    {key === 'action_parser' && '动作解析器'}
                    {key === 'game_agent' && '游戏代理'}
                    {key === 'fbx_exporter' && 'FBX导出器'}
                    {key === 'addon_manager' && '插件管理器'}
                    {key === 'blender' && 'Blender'}
                    {key === 'mcp_enabled' && 'MCP启用'}
                    {key === 'mcp_server_running' && 'MCP服务器'}
                  </span>
                </div>
                <span className={`font-semibold ${getStatusColor(status)}`}>
                  {getStatusText(status)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Blender Information */}
      {healthStatus?.blender_info && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Blender信息</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">可用性:</span>
              <span className={getStatusColor(healthStatus.blender_info.available)}>
                {getStatusIcon(healthStatus.blender_info.available)} {getStatusText(healthStatus.blender_info.available)}
              </span>
            </div>
            {healthStatus.blender_info.version && (
              <div className="flex justify-between">
                <span className="font-medium">版本:</span>
                <span>{healthStatus.blender_info.version}</span>
              </div>
            )}
            {healthStatus.blender_info.path && (
              <div className="flex justify-between">
                <span className="font-medium">路径:</span>
                <span className="text-sm text-gray-600 break-all">{healthStatus.blender_info.path}</span>
              </div>
            )}
            {healthStatus.blender_info.platform && (
              <div className="flex justify-between">
                <span className="font-medium">平台:</span>
                <span>{healthStatus.blender_info.platform}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* MCP Status */}
      {healthStatus?.mcp_status && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">MCP状态</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">MCP启用:</span>
              <span className={getStatusColor(healthStatus.mcp_status.enabled)}>
                {getStatusIcon(healthStatus.mcp_status.enabled)} {getStatusText(healthStatus.mcp_status.enabled)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">服务器运行:</span>
              <span className={getStatusColor(healthStatus.mcp_status.server_running)}>
                {getStatusIcon(healthStatus.mcp_status.server_running)} {getStatusText(healthStatus.mcp_status.server_running)}
              </span>
            </div>
            {healthStatus.mcp_status.server_url && (
              <div className="flex justify-between">
                <span className="font-medium">服务器URL:</span>
                <span className="text-sm text-gray-600">{healthStatus.mcp_status.server_url}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* API Endpoints */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">API端点</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">核心功能</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• POST /generate-actions - 生成动作命令</li>
              <li>• POST /export-fbx - 导出FBX文件</li>
              <li>• GET /health - 健康检查</li>
              <li>• GET /output-files - 文件列表</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">MCP功能</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• POST /mcp-server - MCP服务器管理</li>
              <li>• GET /scene-info - 场景信息</li>
              <li>• POST /execute-blender-code - 执行代码</li>
              <li>• POST /manage-addon - 插件管理</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
