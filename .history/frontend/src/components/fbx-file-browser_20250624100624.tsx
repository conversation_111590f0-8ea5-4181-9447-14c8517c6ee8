'use client';

import React, { useState, useEffect } from 'react';
import AnimationPreview from './animation-preview';

interface FBXFile {
  name: string;
  path: string;
  size: number;
  modified: string;
  download_url: string;
}

interface FBXFileBrowserProps {
  apiBaseUrl?: string;
  className?: string;
}

export default function FBXFileBrowser({ 
  apiBaseUrl = "http://localhost:8000",
  className = ""
}: FBXFileBrowserProps) {
  const [fbxFiles, setFbxFiles] = useState<FBXFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<FBXFile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // 获取 FBX 文件列表
  const fetchFBXFiles = async () => {
    try {
      setRefreshing(true);
      const response = await fetch(`${apiBaseUrl}/output-files?file_type=fbx`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setFbxFiles(data.files || []);
      setError(null);

      // 如果有文件且没有选中的文件，自动选中第一个
      if (data.files && data.files.length > 0 && !selectedFile) {
        setSelectedFile(data.files[0]);
      }
    } catch (err) {
      console.error('Error fetching FBX files:', err);
      if (err instanceof TypeError && err.message.includes('fetch')) {
        setError('Backend server is not running. Please start the backend server on port 8000.');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch FBX files');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchFBXFiles();
  }, [apiBaseUrl]);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化修改时间
  const formatModifiedTime = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading FBX files...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Files</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchFBXFiles}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Generated FBX Files ({fbxFiles.length})
          </h2>
          <button
            onClick={fetchFBXFiles}
            disabled={refreshing}
            className="flex items-center space-x-2 bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <svg 
              className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {fbxFiles.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No FBX Files Found</h3>
          <p className="text-gray-600">Generate some animations to see them here.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
          {/* File List */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900 mb-3">File List</h3>
            <div className="max-h-96 overflow-y-auto space-y-2">
              {fbxFiles.map((file, index) => (
                <div
                  key={index}
                  onClick={() => setSelectedFile(file)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedFile?.name === file.name
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </h4>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>{formatFileSize(file.size)}</span>
                        <span>{formatModifiedTime(file.modified)}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-2">
                      <a
                        href={`${apiBaseUrl}${file.download_url}`}
                        download
                        onClick={(e) => e.stopPropagation()}
                        className="text-blue-600 hover:text-blue-800 transition-colors"
                        title="Download"
                      >
                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Preview */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Preview</h3>
            {selectedFile ? (
              <AnimationPreview
                fbxUrl={`${apiBaseUrl}${selectedFile.download_url}`}
                animationName={selectedFile.name}
                autoPlay={true}
                showGrid={true}
                showStats={false}
                cameraPosition={[5, 5, 5]}
                backgroundColor="#f8fafc"
              />
            ) : (
              <div className="w-full h-96 border border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                <div className="text-center text-gray-500">
                  <svg className="mx-auto h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <p>Select a file to preview</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
