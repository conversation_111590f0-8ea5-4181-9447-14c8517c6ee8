'use client';

import { useState } from 'react';
import AnimationPreview from '@/components/animation-preview';

interface GeneratedFBX {
  url: string;
  name: string;
  actions: any[];
}

export default function ConversationPage() {
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedFBX, setGeneratedFBX] = useState<GeneratedFBX | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<string>('');

  const API_BASE_URL = 'http://localhost:8000';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputText.trim()) return;

    setLoading(true);
    setError(null);
    setSuccess(null);
    setCurrentStep('正在解析自然语言...');

    try {
      // 第一步：生成动作
      const actionsResponse = await fetch(`${API_BASE_URL}/parse`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          use_llm: true,
          model_name: "gpt-4o-mini",
          temperature: 0.1
        }),
      });

      if (!actionsResponse.ok) {
        throw new Error(`生成动作失败: ${actionsResponse.status}`);
      }

      const actionsData = await actionsResponse.json();
      
      if (!actionsData.success || !actionsData.actions || actionsData.actions.length === 0) {
        throw new Error('生成的动作为空或无效');
      }

      setCurrentStep('正在生成FBX文件...');

      // 第二步：导出FBX
      const fbxResponse = await fetch(`${API_BASE_URL}/export-fbx`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          actions: actionsData.actions,
          project_name: `Conversation_${Date.now()}`,
          character_name: "小女孩",
          output_filename: `conversation_${Date.now()}.fbx`
        }),
      });

      if (!fbxResponse.ok) {
        throw new Error(`导出FBX失败: ${fbxResponse.status}`);
      }

      const fbxData = await fbxResponse.json();

      if (!fbxData.export_success || !fbxData.download_url) {
        throw new Error('FBX导出失败或下载链接无效');
      }

      // 设置生成的FBX信息
      setGeneratedFBX({
        url: `${API_BASE_URL}${fbxData.download_url}`,
        name: fbxData.output_file_path?.split('/').pop() || 'Generated Animation',
        actions: actionsData.actions
      });

      setSuccess(`成功生成动画！包含 ${actionsData.actions.length} 个动作`);

    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : '生成动画时发生未知错误');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setInputText('');
    setGeneratedFBX(null);
    setError(null);
    setSuccess(null);
  };

  return (
    <div className="space-y-6">
      {/* Input Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          动作描述输入
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="action-input" className="block text-sm font-medium text-gray-700 mb-2">
              请描述您想要的动作（例如：一个小女孩挥手3秒，然后向前五步走，并跳跃，向左移动10步，然后蹲下）
            </label>
            <textarea
              id="action-input"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="输入动作描述..."
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              rows={4}
              disabled={loading}
            />
          </div>

          <div className="flex space-x-3">
            <button
              type="submit"
              disabled={loading || !inputText.trim()}
              className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  生成中...
                </>
              ) : (
                '生成动画'
              )}
            </button>

            <button
              type="button"
              onClick={handleClear}
              disabled={loading}
              className="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              清空
            </button>
          </div>
        </form>

        {/* Status Messages */}
        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <strong>错误：</strong> {error}
          </div>
        )}

        {success && (
          <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <strong>成功：</strong> {success}
          </div>
        )}
      </div>

      {/* Preview Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          实时预览
        </h2>

        {generatedFBX ? (
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              <p><strong>文件名：</strong> {generatedFBX.name}</p>
              <p><strong>动作数量：</strong> {generatedFBX.actions.length}</p>
            </div>
            
            <AnimationPreview
              fbxUrl={generatedFBX.url}
              animationName={generatedFBX.name}
              autoPlay={true}
              showGrid={true}
              showStats={false}
              cameraPosition={[5, 5, 5]}
              backgroundColor="#f8fafc"
            />
          </div>
        ) : (
          <div className="h-96 flex items-center justify-center bg-gray-100 rounded-lg">
            <div className="text-center text-gray-500">
              <p className="text-lg mb-2">暂无预览内容</p>
              <p className="text-sm">请在上方输入动作描述并点击"生成动画"来预览结果</p>
            </div>
          </div>
        )}
      </div>

      {/* Instructions and Examples */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">
            使用说明
          </h3>
          <ul className="text-blue-700 space-y-2">
            <li>• 在文本框中输入自然语言描述的动作序列</li>
            <li>• 系统会自动将文本转换为动作命令，然后生成FBX文件</li>
            <li>• 生成完成后会在下方实时预览3D动画</li>
            <li>• 支持的动作包括：走路、跑步、跳跃、挥手、蹲下等</li>
            <li>• 可以指定时间、方向、距离等参数</li>
          </ul>
        </div>

        <div className="bg-green-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-green-800 mb-3">
            示例文本
          </h3>
          <div className="space-y-3">
            <button
              onClick={() => setInputText("一个小女孩挥手3秒，然后向前五步走，并跳跃，向左移动10步，然后蹲下")}
              className="w-full text-left p-3 bg-white rounded border hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm text-green-700">
                "一个小女孩挥手3秒，然后向前五步走，并跳跃，向左移动10步，然后蹲下"
              </div>
            </button>
            <button
              onClick={() => setInputText("角色向前跑步5秒，然后停下来挥手打招呼")}
              className="w-full text-left p-3 bg-white rounded border hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm text-green-700">
                "角色向前跑步5秒，然后停下来挥手打招呼"
              </div>
            </button>
            <button
              onClick={() => setInputText("角色蹲下2秒，然后站起来跳跃3次")}
              className="w-full text-left p-3 bg-white rounded border hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm text-green-700">
                "角色蹲下2秒，然后站起来跳跃3次"
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
