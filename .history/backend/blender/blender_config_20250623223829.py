"""
Blender 配置管理器
自动检测和配置 Blender 环境
"""

import os
import json
import platform
import subprocess
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger


class BlenderConfig:
    """Blender 配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file or "blender_config.json"
        self.system = platform.system().lower()
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "blender_executable": None,
            "auto_detect": True,
            "search_paths": self._get_default_search_paths(),
            "version_preference": ["4.0", "3.6", "3.5", "3.4", "3.3"],
            "export_settings": {
                "global_scale": 1.0,
                "apply_unit_scale": True,
                "use_space_transform": True,
                "bake_anim": True,
                "bake_anim_use_all_bones": True,
                "bake_anim_step": 1.0
            },
            "mcp_settings": {
                "enabled": True,
                "server_host": "localhost",
                "server_port": 9876,
                "auto_start_server": True,
                "addon_auto_install": True,
                "addon_path": None,
                "connection_timeout": 15.0,
                "max_retries": 3,
                "features": {
                    "scene_info": True,
                    "viewport_screenshot": True,
                    "code_execution": True,
                    "advanced_operations": True,
                    "asset_integration": False,
                    "polyhaven_integration": False,
                    "sketchfab_integration": False,
                    "hyper3d_integration": False
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}")
        
        return default_config
    
    def _get_default_search_paths(self) -> List[str]:
        """获取默认搜索路径"""
        if self.system == "darwin":  # macOS
            return [
                "/Applications/Blender.app/Contents/MacOS/Blender",
                "/Applications/Blender 4.0/Blender.app/Contents/MacOS/Blender",
                "/Applications/Blender 3.6/Blender.app/Contents/MacOS/Blender",
                "/Applications/Blender 3.5/Blender.app/Contents/MacOS/Blender",
                "/Applications/Blender 3.4/Blender.app/Contents/MacOS/Blender",
                "/Applications/Blender 3.3/Blender.app/Contents/MacOS/Blender",
                "/usr/local/bin/blender",
                "/opt/homebrew/bin/blender",
                "blender"
            ]
        elif self.system == "windows":
            return [
                "C:\\Program Files\\Blender Foundation\\Blender 4.0\\blender.exe",
                "C:\\Program Files\\Blender Foundation\\Blender 3.6\\blender.exe",
                "C:\\Program Files\\Blender Foundation\\Blender 3.5\\blender.exe",
                "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe",
                "C:\\Program Files (x86)\\Blender Foundation\\Blender\\blender.exe",
                "blender.exe"
            ]
        else:  # Linux
            return [
                "/usr/bin/blender",
                "/usr/local/bin/blender",
                "/opt/blender/blender",
                "/snap/bin/blender",
                "blender"
            ]
    
    def detect_blender(self) -> Optional[str]:
        """自动检测 Blender 安装"""
        logger.info(f"正在检测 {self.system} 系统上的 Blender 安装...")
        
        # 如果配置中指定了路径且存在，直接使用
        if self.config.get("blender_executable"):
            path = self.config["blender_executable"]
            if self._test_blender_path(path):
                logger.info(f"使用配置的 Blender 路径: {path}")
                return path
        
        # 自动检测
        if self.config.get("auto_detect", True):
            search_paths = self.config.get("search_paths", self._get_default_search_paths())
            
            for path in search_paths:
                if self._test_blender_path(path):
                    logger.info(f"检测到 Blender: {path}")
                    return path
        
        logger.warning("未检测到 Blender 安装")
        return None
    
    def _test_blender_path(self, path: str) -> bool:
        """测试 Blender 路径是否有效"""
        try:
            # 检查文件是否存在
            if path not in ["blender", "blender.exe"] and not os.path.exists(path):
                return False
            
            # 尝试运行 Blender 获取版本信息
            result = subprocess.run(
                [path, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                version_info = result.stdout.strip()
                logger.debug(f"Blender 版本信息: {version_info}")
                return True
            
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            logger.debug(f"测试 Blender 路径失败 {path}: {e}")
        
        return False
    
    def get_blender_info(self, blender_path: str) -> Dict[str, Any]:
        """获取 Blender 详细信息"""
        try:
            result = subprocess.run(
                [blender_path, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                version_output = result.stdout.strip()
                
                # 解析版本信息
                version_line = version_output.split('\n')[0]
                version = "Unknown"
                
                if "Blender" in version_line:
                    parts = version_line.split()
                    for part in parts:
                        if part.replace('.', '').isdigit():
                            version = part
                            break
                
                return {
                    "available": True,
                    "path": blender_path,
                    "version": version,
                    "full_version_info": version_output,
                    "system": self.system
                }
            
        except Exception as e:
            logger.error(f"获取 Blender 信息失败: {e}")
        
        return {
            "available": False,
            "error": "Failed to get Blender information"
        }
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def set_blender_path(self, path: str) -> bool:
        """设置 Blender 路径"""
        if self._test_blender_path(path):
            self.config["blender_executable"] = path
            self.save_config()
            logger.info(f"Blender 路径已设置为: {path}")
            return True
        else:
            logger.error(f"无效的 Blender 路径: {path}")
            return False
    
    def get_export_settings(self) -> Dict[str, Any]:
        """获取导出设置"""
        return self.config.get("export_settings", {})
    
    def update_export_settings(self, settings: Dict[str, Any]) -> None:
        """更新导出设置"""
        if "export_settings" not in self.config:
            self.config["export_settings"] = {}
        
        self.config["export_settings"].update(settings)
        self.save_config()
    
    def get_recommended_blender_path(self) -> str:
        """获取推荐的 Blender 安装路径"""
        if self.system == "darwin":
            return "/Applications/Blender.app/Contents/MacOS/Blender"
        elif self.system == "windows":
            return "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe"
        else:
            return "/usr/bin/blender"
    
    def install_instructions(self) -> Dict[str, str]:
        """获取安装说明"""
        if self.system == "darwin":
            return {
                "method": "官方下载",
                "url": "https://www.blender.org/download/",
                "instructions": "1. 从官网下载 macOS 版本\n2. 拖拽到 Applications 文件夹\n3. 或使用 Homebrew: brew install --cask blender",
                "typical_path": "/Applications/Blender.app/Contents/MacOS/Blender"
            }
        elif self.system == "windows":
            return {
                "method": "官方下载或 Microsoft Store",
                "url": "https://www.blender.org/download/",
                "instructions": "1. 从官网下载 Windows 版本\n2. 运行安装程序\n3. 或从 Microsoft Store 安装",
                "typical_path": "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe"
            }
        else:
            return {
                "method": "包管理器或官方下载",
                "url": "https://www.blender.org/download/",
                "instructions": "Ubuntu/Debian: sudo apt install blender\nFedora: sudo dnf install blender\nArch: sudo pacman -S blender",
                "typical_path": "/usr/bin/blender"
            }


# 全局配置实例
blender_config = BlenderConfig()
