"""
Blender MCP Addon for Motion Agent
Based on the original blender-mcp addon but integrated with motion-agent workflow
"""

import bpy
import mathutils
import json
import threading
import socket
import time
import requests
import tempfile
import traceback
import os
import shutil
import zipfile
from bpy.props import String<PERSON><PERSON>ty, <PERSON>t<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ty, EnumProperty
import io
from contextlib import redirect_stdout, suppress

bl_info = {
    "name": "Motion Agent MCP",
    "author": "Motion Agent Team",
    "version": (1, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > Motion Agent MCP",
    "description": "Connect Blender to Motion Agent via MCP",
    "category": "Interface",
}


class MotionAgentMCPServer:
    def __init__(self, host='localhost', port=9876):
        self.host = host
        self.port = port
        self.running = False
        self.socket = None
        self.server_thread = None

    def start(self):
        if self.running:
            print("Server is already running")
            return

        self.running = True
        try:
            # Create socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.socket.listen(1)

            # Start server thread
            self.server_thread = threading.Thread(target=self._server_loop)
            self.server_thread.daemon = True
            self.server_thread.start()

            print(f"Motion Agent MCP server started on {self.host}:{self.port}")
        except Exception as e:
            print(f"Failed to start server: {str(e)}")
            self.stop()

    def stop(self):
        self.running = False
        
        # Close socket
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

        # Wait for thread to finish
        if self.server_thread:
            try:
                if self.server_thread.is_alive():
                    self.server_thread.join(timeout=1.0)
            except:
                pass
            self.server_thread = None

        print("Motion Agent MCP server stopped")

    def _server_loop(self):
        """Main server loop in a separate thread"""
        print("Server thread started")
        self.socket.settimeout(1.0)
        
        while self.running:
            try:
                try:
                    client, address = self.socket.accept()
                    print(f"Connected to client: {address}")
                    
                    # Handle client in a separate thread
                    client_thread = threading.Thread(
                        target=self._handle_client, 
                        args=(client,)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"Error accepting connection: {str(e)}")
                    time.sleep(0.5)
            except Exception as e:
                print(f"Error in server loop: {str(e)}")
                if not self.running:
                    break
                time.sleep(0.5)

        print("Server thread stopped")

    def _handle_client(self, client):
        """Handle connected client"""
        print("Client handler started")
        client.settimeout(None)
        buffer = b''
        
        try:
            while self.running:
                try:
                    data = client.recv(8192)
                    if not data:
                        print("Client disconnected")
                        break
                    
                    buffer += data
                    
                    try:
                        # Try to parse command
                        command = json.loads(buffer.decode('utf-8'))
                        buffer = b''
                        
                        # Execute command in Blender's main thread
                        def execute_wrapper():
                            try:
                                response = self.execute_command(command)
                                response_json = json.dumps(response)
                                try:
                                    client.sendall(response_json.encode('utf-8'))
                                except:
                                    print("Failed to send response - client disconnected")
                            except Exception as e:
                                print(f"Error executing command: {str(e)}")
                                traceback.print_exc()
                                try:
                                    error_response = {
                                        "status": "error",
                                        "message": str(e)
                                    }
                                    client.sendall(json.dumps(error_response).encode('utf-8'))
                                except:
                                    pass
                            return None

                        # Schedule execution in main thread
                        bpy.app.timers.register(execute_wrapper, first_interval=0.0)
                        
                    except json.JSONDecodeError:
                        # Incomplete data, wait for more
                        pass
                        
                except Exception as e:
                    print(f"Error receiving data: {str(e)}")
                    break
                    
        except Exception as e:
            print(f"Error in client handler: {str(e)}")
        finally:
            try:
                client.close()
            except:
                pass
            print("Client handler stopped")

    def execute_command(self, command):
        """Execute a command in the main Blender thread"""
        try:
            return self._execute_command_internal(command)
        except Exception as e:
            print(f"Error executing command: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    def _execute_command_internal(self, command):
        """Internal command execution with proper context"""
        cmd_type = command.get("type")
        params = command.get("params", {})

        # Basic handlers
        handlers = {
            "get_scene_info": self.get_scene_info,
            "get_object_info": self.get_object_info,
            "get_viewport_screenshot": self.get_viewport_screenshot,
            "execute_code": self.execute_code,
            "create_animation": self.create_animation,
            "apply_motion_data": self.apply_motion_data,
        }

        handler = handlers.get(cmd_type)
        if handler:
            try:
                print(f"Executing handler for {cmd_type}")
                result = handler(**params)
                print(f"Handler execution complete")
                return {"status": "success", "result": result}
            except Exception as e:
                print(f"Error in handler: {str(e)}")
                traceback.print_exc()
                return {"status": "error", "message": str(e)}
        else:
            return {"status": "error", "message": f"Unknown command type: {cmd_type}"}

    def get_scene_info(self):
        """Get information about the current Blender scene"""
        try:
            print("Getting scene info...")
            scene_info = {
                "name": bpy.context.scene.name,
                "object_count": len(bpy.context.scene.objects),
                "objects": [],
                "materials_count": len(bpy.data.materials),
                "frame_current": bpy.context.scene.frame_current,
                "frame_start": bpy.context.scene.frame_start,
                "frame_end": bpy.context.scene.frame_end,
            }

            # Collect object information
            for i, obj in enumerate(bpy.context.scene.objects):
                if i >= 20:  # Limit to 20 objects
                    break
                obj_info = {
                    "name": obj.name,
                    "type": obj.type,
                    "location": [round(float(obj.location.x), 2), 
                               round(float(obj.location.y), 2), 
                               round(float(obj.location.z), 2)],
                    "visible": obj.visible_get(),
                }
                scene_info["objects"].append(obj_info)

            print(f"Scene info collected: {len(scene_info['objects'])} objects")
            return scene_info
        except Exception as e:
            print(f"Error in get_scene_info: {str(e)}")
            traceback.print_exc()
            return {"error": str(e)}

    def get_object_info(self, name):
        """Get detailed information about a specific object"""
        obj = bpy.data.objects.get(name)
        if not obj:
            raise ValueError(f"Object not found: {name}")

        obj_info = {
            "name": obj.name,
            "type": obj.type,
            "location": [obj.location.x, obj.location.y, obj.location.z],
            "rotation": [obj.rotation_euler.x, obj.rotation_euler.y, obj.rotation_euler.z],
            "scale": [obj.scale.x, obj.scale.y, obj.scale.z],
            "visible": obj.visible_get(),
            "materials": [],
        }

        # Add material slots
        for slot in obj.material_slots:
            if slot.material:
                obj_info["materials"].append(slot.material.name)

        # Add mesh data if applicable
        if obj.type == 'MESH' and obj.data:
            mesh = obj.data
            obj_info["mesh"] = {
                "vertices": len(mesh.vertices),
                "edges": len(mesh.edges),
                "polygons": len(mesh.polygons),
            }

        return obj_info

    def get_viewport_screenshot(self, max_size=800, filepath=None, format="png"):
        """Capture a screenshot of the current 3D viewport"""
        try:
            if not filepath:
                return {"error": "No filepath provided"}

            # Find the active 3D viewport
            area = None
            for a in bpy.context.screen.areas:
                if a.type == 'VIEW_3D':
                    area = a
                    break

            if not area:
                return {"error": "No 3D viewport found"}

            # Take screenshot
            with bpy.context.temp_override(area=area):
                bpy.ops.screen.screenshot_area(filepath=filepath)

            return {
                "success": True,
                "filepath": filepath
            }

        except Exception as e:
            return {"error": str(e)}

    def execute_code(self, code):
        """Execute arbitrary Blender Python code"""
        try:
            namespace = {"bpy": bpy}
            capture_buffer = io.StringIO()
            
            with redirect_stdout(capture_buffer):
                exec(code, namespace)
            
            captured_output = capture_buffer.getvalue()
            return {"executed": True, "result": captured_output}
        except Exception as e:
            raise Exception(f"Code execution error: {str(e)}")

    def create_animation(self, project_name="MotionAgentAnimation", character_name="Character"):
        """Create a basic animation setup"""
        try:
            # Clear existing mesh objects
            bpy.ops.object.select_all(action='DESELECT')
            bpy.ops.object.select_by_type(type='MESH')
            bpy.ops.object.delete(use_global=False)

            # Create a basic character (cube for now)
            bpy.ops.mesh.primitive_cube_add(location=(0, 0, 1))
            character = bpy.context.active_object
            character.name = character_name

            # Set up basic animation
            character.keyframe_insert(data_path="location", frame=1)
            character.location.z = 2
            character.keyframe_insert(data_path="location", frame=50)

            return {
                "success": True,
                "character_name": character.name,
                "message": f"Basic animation setup created for {character_name}"
            }

        except Exception as e:
            return {"error": str(e)}

    def apply_motion_data(self, motion_data, character_name="Character"):
        """Apply motion data to a character"""
        try:
            # Get the character object
            character = bpy.data.objects.get(character_name)
            if not character:
                return {"error": f"Character '{character_name}' not found"}

            # Clear existing keyframes
            character.animation_data_clear()

            # Apply motion data
            for frame_data in motion_data:
                frame = frame_data.get("frame", 1)
                location = frame_data.get("location", [0, 0, 0])
                rotation = frame_data.get("rotation", [0, 0, 0])

                # Set location
                character.location = location
                character.keyframe_insert(data_path="location", frame=frame)

                # Set rotation
                character.rotation_euler = rotation
                character.keyframe_insert(data_path="rotation_euler", frame=frame)

            return {
                "success": True,
                "frames_applied": len(motion_data),
                "message": f"Motion data applied to {character_name}"
            }

        except Exception as e:
            return {"error": str(e)}


# Blender UI Panel
class MOTIONAGENTMCP_PT_Panel(bpy.types.Panel):
    bl_label = "Motion Agent MCP"
    bl_idname = "MOTIONAGENTMCP_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Motion Agent'

    def draw(self, context):
        layout = self.layout
        scene = context.scene

        layout.prop(scene, "motionagent_mcp_port")

        if not scene.motionagent_mcp_server_running:
            layout.operator("motionagentmcp.start_server", text="Start MCP Server")
        else:
            layout.operator("motionagentmcp.stop_server", text="Stop MCP Server")
            layout.label(text=f"Running on port {scene.motionagent_mcp_port}")


# Operator to start the server
class MOTIONAGENTMCP_OT_StartServer(bpy.types.Operator):
    bl_idname = "motionagentmcp.start_server"
    bl_label = "Start Motion Agent MCP Server"
    bl_description = "Start the Motion Agent MCP server"

    def execute(self, context):
        scene = context.scene

        # Create a new server instance
        if not hasattr(bpy.types, "motionagent_mcp_server") or not bpy.types.motionagent_mcp_server:
            bpy.types.motionagent_mcp_server = MotionAgentMCPServer(port=scene.motionagent_mcp_port)

        # Start the server
        bpy.types.motionagent_mcp_server.start()
        scene.motionagent_mcp_server_running = True

        return {'FINISHED'}


# Operator to stop the server
class MOTIONAGENTMCP_OT_StopServer(bpy.types.Operator):
    bl_idname = "motionagentmcp.stop_server"
    bl_label = "Stop Motion Agent MCP Server"
    bl_description = "Stop the Motion Agent MCP server"

    def execute(self, context):
        scene = context.scene

        # Stop the server if it exists
        if hasattr(bpy.types, "motionagent_mcp_server") and bpy.types.motionagent_mcp_server:
            bpy.types.motionagent_mcp_server.stop()
            del bpy.types.motionagent_mcp_server

        scene.motionagent_mcp_server_running = False

        return {'FINISHED'}


# Registration functions
def register():
    bpy.types.Scene.motionagent_mcp_port = IntProperty(
        name="Port",
        description="Port for the Motion Agent MCP server",
        default=9876,
        min=1024,
        max=65535
    )

    bpy.types.Scene.motionagent_mcp_server_running = bpy.props.BoolProperty(
        name="Server Running",
        default=False
    )

    bpy.utils.register_class(MOTIONAGENTMCP_PT_Panel)
    bpy.utils.register_class(MOTIONAGENTMCP_OT_StartServer)
    bpy.utils.register_class(MOTIONAGENTMCP_OT_StopServer)

    print("Motion Agent MCP addon registered")


def unregister():
    # Stop the server if it's running
    if hasattr(bpy.types, "motionagent_mcp_server") and bpy.types.motionagent_mcp_server:
        bpy.types.motionagent_mcp_server.stop()
        del bpy.types.motionagent_mcp_server

    bpy.utils.unregister_class(MOTIONAGENTMCP_PT_Panel)
    bpy.utils.unregister_class(MOTIONAGENTMCP_OT_StartServer)
    bpy.utils.unregister_class(MOTIONAGENTMCP_OT_StopServer)

    del bpy.types.Scene.motionagent_mcp_port
    del bpy.types.Scene.motionagent_mcp_server_running

    print("Motion Agent MCP addon unregistered")


if __name__ == "__main__":
    register()
