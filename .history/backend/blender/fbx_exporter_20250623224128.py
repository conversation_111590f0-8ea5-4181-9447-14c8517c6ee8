"""
FBX 导出功能
处理 Blender 调用和 FBX 文件生成
"""

import os
import json
import subprocess
import tempfile
import shutil
import platform
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import uuid
from loguru import logger

from backend.blender.converter import GameActionToBlenderConverter
from backend.blender.mcp_enhanced_converter import MCPEnhancedConverter
from backend.blender.animation_schema import BlenderProject
from backend.blender.blender_config import BlenderConfig
from backend.blender.mcp_server import get_mcp_manager
from backend.utils.file import save_json_to_output


def get_default_blender_path() -> str:
    """获取默认的 Blender 可执行文件路径

    Returns:
        str: Blender 可执行文件路径
    """
    system = platform.system().lower()

    if system == "darwin":  # macOS
        # macOS 常见的 Blender 安装路径
        possible_paths = [
            "/Applications/Blender.app/Contents/MacOS/Blender",
            "/Applications/Blender 4.0/Blender.app/Contents/MacOS/Blender",
            "/Applications/Blender 3.6/Blender.app/Contents/MacOS/Blender",
            "/Applications/Blender 3.5/Blender.app/Contents/MacOS/Blender",
            "/Applications/Blender 3.4/Blender.app/Contents/MacOS/Blender",
            "/Applications/Blender 3.3/Blender.app/Contents/MacOS/Blender",
            "/usr/local/bin/blender",
            "blender"  # 如果在 PATH 中
        ]
    elif system == "windows":  # Windows
        possible_paths = [
            "C:\\Program Files\\Blender Foundation\\Blender 4.0\\blender.exe",
            "C:\\Program Files\\Blender Foundation\\Blender 3.6\\blender.exe",
            "C:\\Program Files\\Blender Foundation\\Blender 3.5\\blender.exe",
            "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe",
            "blender.exe"
        ]
    else:  # Linux
        possible_paths = [
            "/usr/bin/blender",
            "/usr/local/bin/blender",
            "/opt/blender/blender",
            "blender"
        ]

    # 检查哪个路径存在
    for path in possible_paths:
        if os.path.exists(path):
            return path
        elif path in ["blender", "blender.exe"]:
            # 检查是否在 PATH 中
            try:
                result = subprocess.run([path, "--version"],
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue

    # 如果都没找到，返回默认值
    return "blender"


class FBXExporter:
    """FBX 导出器"""

    def __init__(self, blender_executable: Optional[str] = None,
                 temp_dir: Optional[str] = None,
                 config: Optional[BlenderConfig] = None,
                 use_mcp: Optional[bool] = None):
        """初始化 FBX 导出器

        Args:
            blender_executable: Blender 可执行文件路径，None 则自动检测
            temp_dir: 临时目录路径
            config: Blender 配置管理器
            use_mcp: 是否使用MCP增强功能，None则根据配置决定
        """
        self.config = config or BlenderConfig()

        # 确定 Blender 可执行文件路径
        if blender_executable:
            self.blender_executable = blender_executable
        else:
            detected_path = self.config.detect_blender()
            self.blender_executable = detected_path or get_default_blender_path()

        self.temp_dir = temp_dir or tempfile.gettempdir()

        # 确定是否使用MCP
        if use_mcp is None:
            use_mcp = self.config.is_mcp_enabled()

        self.use_mcp = use_mcp

        # 选择转换器
        if self.use_mcp:
            try:
                self.converter = MCPEnhancedConverter()
                logger.info("使用MCP增强转换器")
            except Exception as e:
                logger.warning(f"MCP转换器初始化失败，使用基础转换器: {e}")
                self.converter = GameActionToBlenderConverter()
                self.use_mcp = False
        else:
            self.converter = GameActionToBlenderConverter()
            logger.info("使用基础转换器")

        # 确保临时目录存在
        os.makedirs(self.temp_dir, exist_ok=True)

        logger.info(f"FBX 导出器初始化完成")
        logger.info(f"Blender 路径: {self.blender_executable}")
        logger.info(f"临时目录: {self.temp_dir}")
        logger.info(f"操作系统: {platform.system()}")
        logger.info(f"MCP增强: {self.use_mcp}")

        # 验证 Blender 可用性
        blender_info = self.get_blender_info()
        if blender_info["available"]:
            logger.info(f"Blender 版本: {blender_info.get('version', 'Unknown')}")
        else:
            logger.warning(f"Blender 不可用: {blender_info.get('error', 'Unknown error')}")
    
    def check_blender_availability(self) -> bool:
        """检查 Blender 是否可用
        
        Returns:
            是否可用
        """
        try:
            result = subprocess.run(
                [self.blender_executable, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def export_actions_to_fbx(self, actions: List[Dict[str, Any]], 
                            output_path: str,
                            project_name: str = "MotionAgentAnimation",
                            character_name: str = "Character") -> Dict[str, Any]:
        """将游戏动作导出为 FBX 文件
        
        Args:
            actions: 游戏动作列表
            output_path: 输出 FBX 文件路径
            project_name: 项目名称
            character_name: 角色名称
            
        Returns:
            导出结果
        """
        try:
            logger.info(f"开始导出 {len(actions)} 个动作到 FBX: {output_path}")
            
            # 检查 Blender 可用性
            if not self.check_blender_availability():
                raise RuntimeError(f"Blender 不可用，请检查路径: {self.blender_executable}")
            
            # 转换动作为 Blender 格式
            self.converter.character_name = character_name
            blender_project = self.converter.convert_actions_to_blender(actions, project_name)
            
            # 更新导出路径
            blender_project.export_settings.export_path = output_path
            
            # 创建临时文件
            temp_id = str(uuid.uuid4())
            temp_animation_file = os.path.join(self.temp_dir, f"animation_{temp_id}.json")
            temp_script_file = os.path.join(self.temp_dir, f"script_{temp_id}.py")
            
            try:
                # 保存动画数据到临时文件
                with open(temp_animation_file, 'w', encoding='utf-8') as f:
                    json.dump(blender_project.model_dump(), f, indent=2, ensure_ascii=False)
                
                # 复制 Blender 脚本到临时位置
                script_source = os.path.join(os.path.dirname(__file__), "blender_script.py")
                shutil.copy2(script_source, temp_script_file)
                
                # 确保输出路径是绝对路径
                abs_output_path = os.path.abspath(output_path)

                # 确保输出目录存在
                os.makedirs(os.path.dirname(abs_output_path), exist_ok=True)

                # 调用 Blender
                result = self._run_blender(temp_script_file, temp_animation_file, abs_output_path)
                
                if result["success"]:
                    logger.info(f"FBX 导出成功: {abs_output_path}")

                    # 验证输出文件
                    if os.path.exists(abs_output_path):
                        file_size = os.path.getsize(abs_output_path)
                        logger.info(f"FBX 文件大小: {file_size} 字节")

                        # 保存 Blender 项目数据到输出目录
                        try:
                            blender_json_filename = f"{project_name}_blender_project"
                            blender_json_path = save_json_to_output(
                                blender_project.model_dump(),
                                blender_json_filename,
                                add_timestamp=True
                            )
                            logger.info(f"Blender 项目 JSON 已保存到: {blender_json_path}")
                        except Exception as e:
                            logger.warning(f"保存 Blender 项目 JSON 失败: {e}")

                        return {
                            "success": True,
                            "output_path": abs_output_path,
                            "file_size": file_size,
                            "actions_count": len(actions),
                            "project_name": project_name,
                            "blender_project": blender_project.model_dump()
                        }
                    else:
                        raise RuntimeError("FBX 文件未生成")
                else:
                    raise RuntimeError(f"Blender 执行失败: {result['error']}")
                    
            finally:
                # 清理临时文件
                for temp_file in [temp_animation_file, temp_script_file]:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except OSError:
                            pass
                            
        except Exception as e:
            logger.error(f"FBX 导出失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "output_path": output_path
            }
    
    def _run_blender(self, script_path: str, animation_data_path: str, 
                    output_path: str) -> Dict[str, Any]:
        """运行 Blender 处理
        
        Args:
            script_path: Blender 脚本路径
            animation_data_path: 动画数据文件路径
            output_path: 输出文件路径
            
        Returns:
            执行结果
        """
        try:
            # 构建 Blender 命令
            cmd = [
                self.blender_executable,
                "--background",  # 后台模式
                "--python", script_path,
                "--",  # 分隔符，后面的参数传给脚本
                animation_data_path,
                output_path
            ]
            
            logger.info(f"执行 Blender 命令: {' '.join(cmd)}")
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                cwd=self.temp_dir
            )
            
            if result.returncode == 0:
                logger.info("Blender 执行成功")
                logger.debug(f"Blender 输出: {result.stdout}")
                return {"success": True, "stdout": result.stdout, "stderr": result.stderr}
            else:
                logger.error(f"Blender 执行失败，返回码: {result.returncode}")
                logger.error(f"错误输出: {result.stderr}")
                return {
                    "success": False, 
                    "error": result.stderr or "Unknown error",
                    "stdout": result.stdout,
                    "returncode": result.returncode
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Blender 执行超时")
            return {"success": False, "error": "Blender execution timeout"}
        except Exception as e:
            logger.error(f"执行 Blender 时发生异常: {e}")
            return {"success": False, "error": str(e)}
    
    def get_blender_info(self) -> Dict[str, Any]:
        """获取 Blender 信息

        Returns:
            Blender 信息
        """
        return self.config.get_blender_info(self.blender_executable)
    
    def create_preview_animation(self, actions: List[Dict[str, Any]],
                               project_name: str = "Preview") -> BlenderProject:
        """创建预览动画数据（不导出 FBX）
        
        Args:
            actions: 游戏动作列表
            project_name: 项目名称
            
        Returns:
            Blender 项目数据
        """
        return self.converter.convert_actions_to_blender(actions, project_name)
    
    def validate_actions_for_export(self, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证动作是否适合导出
        
        Args:
            actions: 游戏动作列表
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        
        if not actions:
            errors.append("动作列表为空")
            return {"valid": False, "errors": errors, "warnings": warnings}
        
        # 检查动作格式
        for i, action in enumerate(actions):
            if not isinstance(action, dict):
                errors.append(f"动作 {i+1} 不是有效的字典格式")
                continue
            
            if "action" not in action:
                errors.append(f"动作 {i+1} 缺少 'action' 字段")
            
            if "params" not in action:
                warnings.append(f"动作 {i+1} 缺少 'params' 字段")
        
        # 检查动作序列
        sequences = [action.get("sequence", 0) for action in actions if isinstance(action, dict)]
        if len(set(sequences)) != len(sequences):
            warnings.append("动作序列号有重复")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "actions_count": len(actions)
        }

    def configure_blender_path(self, path: str) -> bool:
        """配置 Blender 路径

        Args:
            path: Blender 可执行文件路径

        Returns:
            是否配置成功
        """
        if self.config.set_blender_path(path):
            self.blender_executable = path
            logger.info(f"Blender 路径已更新为: {path}")
            return True
        return False

    def get_install_instructions(self) -> Dict[str, str]:
        """获取 Blender 安装说明

        Returns:
            安装说明
        """
        return self.config.install_instructions()

    def detect_and_configure_blender(self) -> bool:
        """自动检测并配置 Blender

        Returns:
            是否检测成功
        """
        detected_path = self.config.detect_blender()
        if detected_path:
            self.blender_executable = detected_path
            logger.info(f"自动检测到 Blender: {detected_path}")
            return True
        else:
            logger.warning("未能自动检测到 Blender")
            return False

    def get_export_settings(self) -> Dict[str, Any]:
        """获取导出设置

        Returns:
            导出设置
        """
        return self.config.get_export_settings()

    def update_export_settings(self, settings: Dict[str, Any]) -> None:
        """更新导出设置

        Args:
            settings: 新的导出设置
        """
        self.config.update_export_settings(settings)
        logger.info("导出设置已更新")
