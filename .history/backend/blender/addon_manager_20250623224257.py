"""
Blender Addon Manager
Manages the installation and configuration of Blender addons for Motion Agent
"""

import os
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger

from backend.blender.blender_config import BlenderConfig
from backend.blender.mcp_server import get_mcp_manager


class BlenderAddonManager:
    """Blender插件管理器"""
    
    def __init__(self, config: Optional[BlenderConfig] = None):
        """初始化插件管理器
        
        Args:
            config: Blender配置管理器
        """
        self.config = config or BlenderConfig()
        self.addon_name = "motion_agent_mcp"
        self.addon_bl_info = {
            "name": "Motion Agent MCP",
            "author": "Motion Agent Team",
            "version": (1, 0),
            "blender": (3, 0, 0),
            "location": "View3D > Sidebar > Motion Agent MCP",
            "description": "Connect Blender to Motion Agent via MCP",
            "category": "Interface",
        }
    
    def get_blender_addon_directories(self) -> List[str]:
        """获取Blender插件目录列表"""
        addon_dirs = []
        
        # 通过Blender获取插件目录
        try:
            blender_path = self.config.detect_blender()
            if blender_path:
                # 运行Blender脚本获取插件目录
                script = """
import bpy
import addon_utils
import os

# 获取用户插件目录
user_addon_dir = bpy.utils.user_resource('SCRIPTS', "addons")
print(f"USER_ADDON_DIR:{user_addon_dir}")

# 获取系统插件目录
for path in addon_utils.paths():
    print(f"ADDON_PATH:{path}")
"""
                
                result = subprocess.run(
                    [blender_path, "--background", "--python-expr", script],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if line.startswith("USER_ADDON_DIR:"):
                            addon_dirs.append(line.replace("USER_ADDON_DIR:", "").strip())
                        elif line.startswith("ADDON_PATH:"):
                            addon_dirs.append(line.replace("ADDON_PATH:", "").strip())
                
        except Exception as e:
            logger.warning(f"无法通过Blender获取插件目录: {e}")
        
        # 添加默认目录作为备选
        import platform
        system = platform.system().lower()
        
        if system == "darwin":  # macOS
            default_dirs = [
                os.path.expanduser("~/Library/Application Support/Blender/*/scripts/addons"),
                "/Applications/Blender.app/Contents/Resources/*/scripts/addons"
            ]
        elif system == "windows":
            default_dirs = [
                os.path.expanduser("~/AppData/Roaming/Blender Foundation/Blender/*/scripts/addons"),
                "C:/Program Files/Blender Foundation/Blender/*/scripts/addons"
            ]
        else:  # Linux
            default_dirs = [
                os.path.expanduser("~/.config/blender/*/scripts/addons"),
                "/usr/share/blender/*/scripts/addons"
            ]
        
        # 展开通配符路径
        import glob
        for pattern in default_dirs:
            addon_dirs.extend(glob.glob(pattern))
        
        # 去重并过滤存在的目录
        unique_dirs = []
        for dir_path in addon_dirs:
            if dir_path and os.path.isdir(dir_path) and dir_path not in unique_dirs:
                unique_dirs.append(dir_path)
        
        return unique_dirs
    
    def get_addon_source_path(self) -> Optional[str]:
        """获取插件源文件路径"""
        # 首先检查配置中的路径
        configured_path = self.config.get_addon_path()
        if configured_path and os.path.exists(configured_path):
            return configured_path
        
        # 检查项目中的默认路径
        project_root = Path(__file__).parent.parent.parent
        default_addon_path = project_root / "backend" / "blender" / "blender_mcp_addon.py"
        
        if default_addon_path.exists():
            return str(default_addon_path)
        
        logger.error("未找到MCP addon源文件")
        return None
    
    def install_addon(self, target_dir: Optional[str] = None) -> Dict[str, Any]:
        """安装插件
        
        Args:
            target_dir: 目标安装目录，None则自动选择
            
        Returns:
            安装结果
        """
        try:
            # 获取源文件路径
            source_path = self.get_addon_source_path()
            if not source_path:
                return {
                    "success": False,
                    "error": "未找到插件源文件"
                }
            
            # 确定目标目录
            if not target_dir:
                addon_dirs = self.get_blender_addon_directories()
                if not addon_dirs:
                    return {
                        "success": False,
                        "error": "未找到Blender插件目录"
                    }
                target_dir = addon_dirs[0]  # 使用第一个找到的目录
            
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)
            
            # 复制插件文件
            target_path = os.path.join(target_dir, f"{self.addon_name}.py")
            shutil.copy2(source_path, target_path)
            
            logger.info(f"插件已安装到: {target_path}")
            
            # 尝试通过MCP启用插件
            enable_result = self._enable_addon_via_mcp()
            
            return {
                "success": True,
                "target_path": target_path,
                "target_dir": target_dir,
                "enabled": enable_result.get("success", False),
                "enable_message": enable_result.get("message", "")
            }
            
        except Exception as e:
            logger.error(f"插件安装失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _enable_addon_via_mcp(self) -> Dict[str, Any]:
        """通过MCP启用插件"""
        try:
            mcp_manager = get_mcp_manager()
            if not mcp_manager.is_server_running():
                return {
                    "success": False,
                    "message": "MCP服务器未运行"
                }
            
            enable_code = f"""
import bpy
import addon_utils

addon_name = "{self.addon_name}"

# 刷新插件列表
bpy.ops.preferences.addon_refresh()

# 启用插件
try:
    bpy.ops.preferences.addon_enable(module=addon_name)
    print(f"插件 {{addon_name}} 已启用")
    
    # 保存用户偏好
    bpy.ops.wm.save_userpref()
    print("用户偏好已保存")
    
    result = "SUCCESS"
except Exception as e:
    print(f"启用插件失败: {{e}}")
    result = f"ERROR: {{e}}"

print(f"ENABLE_RESULT: {{result}}")
"""
            
            result = mcp_manager.execute_blender_code(enable_code)
            if result.get("success"):
                output = result.get("result", "")
                if "ENABLE_RESULT: SUCCESS" in output:
                    return {
                        "success": True,
                        "message": "插件已成功启用"
                    }
                else:
                    error_msg = "未知错误"
                    for line in output.split('\n'):
                        if line.startswith("ENABLE_RESULT: ERROR:"):
                            error_msg = line.replace("ENABLE_RESULT: ERROR:", "").strip()
                            break
                    return {
                        "success": False,
                        "message": f"启用插件失败: {error_msg}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"MCP执行失败: {result.get('error', '未知错误')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"通过MCP启用插件失败: {e}"
            }
    
    def check_addon_status(self) -> Dict[str, Any]:
        """检查插件状态"""
        try:
            # 检查文件是否存在
            addon_dirs = self.get_blender_addon_directories()
            installed_paths = []
            
            for addon_dir in addon_dirs:
                addon_path = os.path.join(addon_dir, f"{self.addon_name}.py")
                if os.path.exists(addon_path):
                    installed_paths.append(addon_path)
            
            # 通过MCP检查启用状态
            enabled_status = self._check_addon_enabled_via_mcp()
            
            return {
                "installed": len(installed_paths) > 0,
                "installed_paths": installed_paths,
                "enabled": enabled_status.get("enabled", False),
                "addon_name": self.addon_name,
                "status_message": enabled_status.get("message", "")
            }
            
        except Exception as e:
            logger.error(f"检查插件状态失败: {e}")
            return {
                "installed": False,
                "enabled": False,
                "error": str(e)
            }
    
    def _check_addon_enabled_via_mcp(self) -> Dict[str, Any]:
        """通过MCP检查插件是否启用"""
        try:
            mcp_manager = get_mcp_manager()
            if not mcp_manager.is_server_running():
                return {
                    "enabled": False,
                    "message": "MCP服务器未运行"
                }
            
            check_code = f"""
import bpy
import addon_utils

addon_name = "{self.addon_name}"

# 检查插件是否启用
is_enabled = addon_utils.check(addon_name)[1]
print(f"ADDON_ENABLED: {{is_enabled}}")

# 获取插件信息
try:
    addon_info = addon_utils.check(addon_name)
    if addon_info[0]:
        print(f"ADDON_INFO: {{addon_info[0]}}")
    else:
        print("ADDON_INFO: Not found")
except Exception as e:
    print(f"ADDON_INFO_ERROR: {{e}}")
"""
            
            result = mcp_manager.execute_blender_code(check_code)
            if result.get("success"):
                output = result.get("result", "")
                enabled = False
                message = "插件状态未知"
                
                for line in output.split('\n'):
                    if line.startswith("ADDON_ENABLED:"):
                        enabled_str = line.replace("ADDON_ENABLED:", "").strip()
                        enabled = enabled_str.lower() == "true"
                    elif line.startswith("ADDON_INFO:"):
                        info = line.replace("ADDON_INFO:", "").strip()
                        if info == "Not found":
                            message = "插件未找到"
                        else:
                            message = f"插件信息: {info}"
                
                return {
                    "enabled": enabled,
                    "message": message
                }
            else:
                return {
                    "enabled": False,
                    "message": f"MCP检查失败: {result.get('error', '未知错误')}"
                }
                
        except Exception as e:
            return {
                "enabled": False,
                "message": f"通过MCP检查插件状态失败: {e}"
            }
    
    def uninstall_addon(self) -> Dict[str, Any]:
        """卸载插件"""
        try:
            # 首先通过MCP禁用插件
            disable_result = self._disable_addon_via_mcp()
            
            # 删除插件文件
            addon_dirs = self.get_blender_addon_directories()
            removed_paths = []
            
            for addon_dir in addon_dirs:
                addon_path = os.path.join(addon_dir, f"{self.addon_name}.py")
                if os.path.exists(addon_path):
                    try:
                        os.remove(addon_path)
                        removed_paths.append(addon_path)
                        logger.info(f"已删除插件文件: {addon_path}")
                    except Exception as e:
                        logger.warning(f"删除插件文件失败 {addon_path}: {e}")
            
            return {
                "success": len(removed_paths) > 0,
                "removed_paths": removed_paths,
                "disabled": disable_result.get("success", False),
                "disable_message": disable_result.get("message", "")
            }
            
        except Exception as e:
            logger.error(f"卸载插件失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _disable_addon_via_mcp(self) -> Dict[str, Any]:
        """通过MCP禁用插件"""
        try:
            mcp_manager = get_mcp_manager()
            if not mcp_manager.is_server_running():
                return {
                    "success": False,
                    "message": "MCP服务器未运行"
                }
            
            disable_code = f"""
import bpy
import addon_utils

addon_name = "{self.addon_name}"

try:
    bpy.ops.preferences.addon_disable(module=addon_name)
    print(f"插件 {{addon_name}} 已禁用")
    
    # 保存用户偏好
    bpy.ops.wm.save_userpref()
    print("用户偏好已保存")
    
    result = "SUCCESS"
except Exception as e:
    print(f"禁用插件失败: {{e}}")
    result = f"ERROR: {{e}}"

print(f"DISABLE_RESULT: {{result}}")
"""
            
            result = mcp_manager.execute_blender_code(disable_code)
            if result.get("success"):
                output = result.get("result", "")
                if "DISABLE_RESULT: SUCCESS" in output:
                    return {
                        "success": True,
                        "message": "插件已成功禁用"
                    }
                else:
                    return {
                        "success": False,
                        "message": "禁用插件失败"
                    }
            else:
                return {
                    "success": False,
                    "message": f"MCP执行失败: {result.get('error', '未知错误')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"通过MCP禁用插件失败: {e}"
            }
    
    def get_installation_guide(self) -> Dict[str, Any]:
        """获取安装指南"""
        addon_dirs = self.get_blender_addon_directories()
        source_path = self.get_addon_source_path()
        
        return {
            "addon_name": self.addon_name,
            "source_path": source_path,
            "target_directories": addon_dirs,
            "recommended_directory": addon_dirs[0] if addon_dirs else None,
            "manual_steps": [
                "1. 确保Blender已安装并可正常运行",
                "2. 启动Motion Agent MCP服务器",
                "3. 使用API调用 /install-mcp-addon 自动安装",
                "4. 或手动复制插件文件到Blender插件目录",
                "5. 在Blender中启用插件: Edit > Preferences > Add-ons"
            ],
            "bl_info": self.addon_bl_info
        }
