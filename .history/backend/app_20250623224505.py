"""
Motion Agent Backend API
提供自然语言到游戏动作命令转换的 REST API 服务
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from loguru import logger
import uvicorn

from backend.agent.animation_agent import GameActionAgent
from backend.agent.action_parser import ActionParser
from backend.blender.fbx_exporter import FBXExporter
from backend.blender.mcp_server import get_mcp_manager, cleanup_mcp_manager
from backend.blender.addon_manager import BlenderAddonManager
from backend.utils.file import (
    load_json_file, save_json_to_output, get_output_file_path,
    ensure_output_directory, list_output_files
)


# 初始化 FastAPI 应用
app = FastAPI(
    title="Motion Agent API",
    description="将自然语言转换为游戏动作命令的 AI Agent API",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
game_agent: Optional[GameActionAgent] = None
action_parser: Optional[ActionParser] = None
fbx_exporter: Optional[FBXExporter] = None
addon_manager: Optional[BlenderAddonManager] = None


# Pydantic 模型
class ParseRequest(BaseModel):
    """解析请求模型"""
    text: str = Field(..., description="要解析的自然语言文本", min_length=1, max_length=1000)
    use_llm: bool = Field(True, description="是否使用 LLM 进行解析")
    model_name: str = Field("gpt-4o-mini", description="使用的模型名称")
    temperature: float = Field(0.1, description="模型温度参数", ge=0.0, le=2.0)


class ParseResponse(BaseModel):
    """解析响应模型"""
    success: bool = Field(..., description="Whether parsing was successful")
    actions: List[Dict[str, Any]] = Field(..., description="List of parsed action commands")
    error: Optional[str] = Field(None, description="Error message if any")
    input_text: str = Field(..., description="Original input text")
    parsing_method: str = Field(..., description="Parsing method used")


class ValidationRequest(BaseModel):
    """验证请求模型"""
    actions: List[Dict[str, Any]] = Field(..., description="要验证的动作命令列表")


class ValidationResponse(BaseModel):
    """验证响应模型"""
    is_valid: bool = Field(..., description="Whether the actions are valid")
    error_messages: List[str] = Field(..., description="List of error messages")
    warning_messages: List[str] = Field(..., description="List of warning messages")
    total_actions: int = Field(..., description="Total number of actions")


class FBXExportRequest(BaseModel):
    """FBX 导出请求模型"""
    actions: List[Dict[str, Any]] = Field(..., description="要导出的动作命令列表")
    project_name: str = Field("MotionAgentAnimation", description="项目名称")
    character_name: str = Field("Character", description="角色名称")
    output_filename: Optional[str] = Field(None, description="输出文件名（不含路径）")


class BlenderConfigRequest(BaseModel):
    """Blender 配置请求模型"""
    blender_path: str = Field(..., description="Blender 可执行文件路径")


class BlenderConfigResponse(BaseModel):
    """Blender 配置响应模型"""
    config_success: bool = Field(..., description="配置是否成功")
    blender_path: str = Field(..., description="Blender 路径")
    blender_info: Dict[str, Any] = Field(..., description="Blender 信息")
    error_message: Optional[str] = Field(None, description="错误信息")


class FBXExportResponse(BaseModel):
    """FBX 导出响应模型"""
    export_success: bool = Field(..., description="Whether export was successful")
    output_file_path: Optional[str] = Field(None, description="Output file path")
    file_size_bytes: Optional[int] = Field(None, description="File size in bytes")
    total_actions: int = Field(..., description="Number of actions processed")
    project_name: str = Field(..., description="Project name")
    error_message: Optional[str] = Field(None, description="Error message if any")
    download_url: Optional[str] = Field(None, description="Download URL")


# MCP相关模型
class MCPServerRequest(BaseModel):
    """MCP服务器请求模型"""
    action: str = Field(..., description="操作类型: start, stop, status")


class MCPServerResponse(BaseModel):
    """MCP服务器响应模型"""
    success: bool = Field(..., description="操作是否成功")
    action: str = Field(..., description="执行的操作")
    server_running: bool = Field(..., description="服务器是否运行")
    message: Optional[str] = Field(None, description="状态消息")
    config: Optional[Dict[str, Any]] = Field(None, description="服务器配置")


class SceneInfoResponse(BaseModel):
    """场景信息响应模型"""
    success: bool = Field(..., description="获取是否成功")
    scene_info: Optional[Dict[str, Any]] = Field(None, description="场景信息")
    error_message: Optional[str] = Field(None, description="错误信息")


class BlenderCodeRequest(BaseModel):
    """Blender代码执行请求模型"""
    code: str = Field(..., description="要执行的Python代码", min_length=1, max_length=10000)


class BlenderCodeResponse(BaseModel):
    """Blender代码执行响应模型"""
    success: bool = Field(..., description="执行是否成功")
    result: Optional[str] = Field(None, description="执行结果")
    error_message: Optional[str] = Field(None, description="错误信息")


class AddonManagementRequest(BaseModel):
    """插件管理请求模型"""
    action: str = Field(..., description="操作类型: install, uninstall, status")
    target_dir: Optional[str] = Field(None, description="安装目标目录")


class AddonManagementResponse(BaseModel):
    """插件管理响应模型"""
    success: bool = Field(..., description="操作是否成功")
    action: str = Field(..., description="执行的操作")
    message: str = Field(..., description="操作结果消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global game_agent, action_parser, fbx_exporter, addon_manager

    logger.info("正在启动 Motion Agent API...")

    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        logger.warning("OPENAI_API_KEY 环境变量未设置，LLM 功能可能无法使用")

    try:
        # 初始化组件
        action_parser = ActionParser()
        logger.info("动作解析器初始化完成")

        # 初始化 FBX 导出器（支持MCP）
        fbx_exporter = FBXExporter()
        blender_info = fbx_exporter.get_blender_info()
        if blender_info["available"]:
            logger.info(f"Blender 可用: {blender_info['version']}")
        else:
            logger.warning(f"Blender 不可用: {blender_info.get('error', 'Unknown error')}")

        # 初始化插件管理器
        addon_manager = BlenderAddonManager()
        logger.info("插件管理器初始化完成")

        # 检查MCP状态
        mcp_status = fbx_exporter.get_mcp_status()
        if mcp_status.get("enabled"):
            logger.info("MCP功能已启用")
            if mcp_status.get("server_running"):
                logger.info("MCP服务器正在运行")
            else:
                logger.info("MCP服务器未运行，可通过API启动")
        else:
            logger.info("MCP功能未启用，使用基础模式")

        # 只有在有 API Key 时才初始化 LLM Agent
        if os.getenv("OPENAI_API_KEY"):
            game_agent = GameActionAgent()
            logger.info("游戏动作 Agent 初始化完成")
        else:
            logger.warning("跳过 LLM Agent 初始化（缺少 API Key）")

        logger.info("Motion Agent API 启动完成")

    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("正在关闭 Motion Agent API...")

    try:
        # 清理MCP资源
        cleanup_mcp_manager()
        logger.info("MCP资源已清理")
    except Exception as e:
        logger.error(f"清理MCP资源失败: {e}")

    logger.info("Motion Agent API 已关闭")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Motion Agent API",
        "version": "1.0.0",
        "status": "running",
        "llm_available": game_agent is not None
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    blender_info = fbx_exporter.get_blender_info() if fbx_exporter else {"available": False}

    return {
        "status": "healthy",
        "components": {
            "action_parser": action_parser is not None,
            "game_agent": game_agent is not None,
            "fbx_exporter": fbx_exporter is not None,
            "blender": blender_info["available"]
        },
        "blender_info": blender_info
    }


@app.get("/actions")
async def get_supported_actions():
    """获取支持的动作列表"""
    try:
        actions = load_json_file("backend/actions/game_action_command_set.json")
        return {
            "success": True,
            "actions": actions,
            "count": len(actions)
        }
    except Exception as e:
        logger.error(f"获取动作列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取动作列表失败: {str(e)}")


@app.post("/parse", response_model=ParseResponse)
async def parse_natural_language(request: ParseRequest):
    """解析自然语言为游戏动作命令"""
    try:
        logger.info(f"收到解析请求: {request.text[:50]}...")

        if request.use_llm and game_agent is not None:
            # 使用 LLM 解析
            logger.info("使用 LLM 进行解析")

            # 如果需要使用不同的模型参数，重新初始化 agent
            if (request.model_name != "gpt-4o-mini" or
                abs(request.temperature - 0.1) > 0.01):
                temp_agent = GameActionAgent(
                    model_name=request.model_name,
                    temperature=request.temperature
                )
                result = await temp_agent.aparse_natural_language(request.text)
            else:
                result = await game_agent.aparse_natural_language(request.text)

            result["parsing_method"] = "llm"
            result["input_text"] = result.pop("input", request.text)

        else:
            # 使用规则解析
            logger.info("使用规则解析器进行解析")

            if action_parser is None:
                raise HTTPException(status_code=500, detail="动作解析器未初始化")

            actions = action_parser.parse_simple_command(request.text)
            result = {
                "success": True,
                "actions": actions,
                "error": None,
                "input_text": request.text,
                "parsing_method": "rule_based"
            }

        logger.info(f"解析完成，生成了 {len(result['actions'])} 个动作")
        return ParseResponse(**result)

    except Exception as e:
        logger.error(f"解析失败: {e}")
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")


@app.post("/validate", response_model=ValidationResponse)
async def validate_actions(request: ValidationRequest):
    """验证动作命令序列"""
    try:
        logger.info(f"收到验证请求，包含 {len(request.actions)} 个动作")

        if action_parser is None:
            raise HTTPException(status_code=500, detail="动作解析器未初始化")

        result = action_parser.validate_command_sequence(request.actions)

        # 转换为英文 key
        validation_result = {
            "is_valid": result["valid"],
            "error_messages": result["errors"],
            "warning_messages": result["warnings"],
            "total_actions": result["command_count"]
        }

        logger.info(f"验证完成，结果: {'有效' if result['valid'] else '无效'}")
        return ValidationResponse(**validation_result)

    except Exception as e:
        logger.error(f"验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")


@app.post("/export-fbx", response_model=FBXExportResponse)
async def export_fbx(request: FBXExportRequest):
    """导出动作为 FBX 文件"""
    try:
        logger.info(f"收到 FBX 导出请求: {request.project_name}, {len(request.actions)} 个动作")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        # 验证动作
        validation = fbx_exporter.validate_actions_for_export(request.actions)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"动作验证失败: {', '.join(validation['errors'])}"
            )

        # 生成输出文件名
        if request.output_filename:
            filename = request.output_filename
            if not filename.endswith('.fbx'):
                filename += '.fbx'
        else:
            filename = f"{request.project_name}.fbx"

        # 使用统一的输出目录
        output_path = get_output_file_path(filename)

        # 同时保存转换的 JSON 数据到输出目录
        json_filename = f"{request.project_name}_actions"
        json_path = save_json_to_output(request.actions, json_filename, add_timestamp=True)
        logger.info(f"动作 JSON 已保存到: {json_path}")

        # 导出 FBX
        result = fbx_exporter.export_actions_to_fbx(
            actions=request.actions,
            output_path=output_path,
            project_name=request.project_name,
            character_name=request.character_name
        )

        if result["success"]:
            # 生成下载链接
            download_url = f"/download/{filename}"

            logger.info(f"FBX 导出成功: {output_path}")

            return FBXExportResponse(
                export_success=True,
                output_file_path=output_path,
                file_size_bytes=result.get("file_size"),
                total_actions=len(request.actions),
                project_name=request.project_name,
                download_url=download_url
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"FBX 导出失败: {result.get('error', 'Unknown error')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"FBX 导出失败: {e}")
        raise HTTPException(status_code=500, detail=f"FBX 导出失败: {str(e)}")


@app.get("/output-files")
async def list_output_files_endpoint(file_type: Optional[str] = None):
    """列出输出目录中的文件

    Args:
        file_type: 文件类型过滤器 ("json", "fbx", 或 None 表示所有文件)
    """
    try:
        # 根据文件类型设置扩展名过滤器
        extension = None
        if file_type == "json":
            extension = ".json"
        elif file_type == "fbx":
            extension = ".fbx"

        files = list_output_files(file_extension=extension)

        # 添加下载链接
        for file_info in files:
            file_info['download_url'] = f"/download/{file_info['name']}"

        return {
            "output_directory": ensure_output_directory(),
            "total_files": len(files),
            "file_type_filter": file_type,
            "files": files
        }

    except Exception as e:
        logger.error(f"列出输出文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"列出输出文件失败: {str(e)}")


@app.get("/download/{filename}")
async def download_file(filename: str):
    """下载输出文件 (支持 FBX 和 JSON)"""
    try:
        # 安全检查文件名
        allowed_extensions = ['.fbx', '.json']
        if not any(filename.endswith(ext) for ext in allowed_extensions) or '/' in filename or '\\' in filename:
            raise HTTPException(status_code=400, detail="无效的文件名")

        file_path = get_output_file_path(filename)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        from fastapi.responses import FileResponse

        # 根据文件类型设置 MIME 类型
        if filename.endswith('.json'):
            media_type = 'application/json'
        else:
            media_type = 'application/octet-stream'

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type=media_type
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件下载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")


@app.get("/blender-info")
async def get_blender_info():
    """获取 Blender 信息"""
    try:
        if fbx_exporter is None:
            return {"available": False, "error": "FBX 导出器未初始化"}

        return fbx_exporter.get_blender_info()

    except Exception as e:
        logger.error(f"获取 Blender 信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取 Blender 信息失败: {str(e)}")


@app.post("/configure-blender", response_model=BlenderConfigResponse)
async def configure_blender(request: BlenderConfigRequest):
    """配置 Blender 路径"""
    try:
        logger.info(f"收到 Blender 配置请求: {request.blender_path}")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        # 配置 Blender 路径
        success = fbx_exporter.configure_blender_path(request.blender_path)

        if success:
            # 获取配置后的 Blender 信息
            blender_info = fbx_exporter.get_blender_info()

            return BlenderConfigResponse(
                config_success=True,
                blender_path=request.blender_path,
                blender_info=blender_info
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"无效的 Blender 路径: {request.blender_path}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置 Blender 失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置 Blender 失败: {str(e)}")


@app.get("/blender-install-guide")
async def get_blender_install_guide():
    """获取 Blender 安装指南"""
    try:
        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        install_info = fbx_exporter.get_install_instructions()
        blender_info = fbx_exporter.get_blender_info()

        return {
            "current_status": blender_info,
            "install_guide": install_info,
            "auto_detect_available": True,
            "recommended_path": fbx_exporter.config.get_recommended_blender_path()
        }

    except Exception as e:
        logger.error(f"获取安装指南失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取安装指南失败: {str(e)}")


@app.post("/detect-blender")
async def detect_blender():
    """自动检测 Blender"""
    try:
        logger.info("开始自动检测 Blender")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        success = fbx_exporter.detect_and_configure_blender()
        blender_info = fbx_exporter.get_blender_info()

        return {
            "detection_success": success,
            "blender_info": blender_info,
            "message": "检测成功" if success else "未检测到 Blender"
        }

    except Exception as e:
        logger.error(f"自动检测 Blender 失败: {e}")
        raise HTTPException(status_code=500, detail=f"自动检测失败: {str(e)}")


# MCP相关端点
@app.post("/mcp-server", response_model=MCPServerResponse)
async def manage_mcp_server(request: MCPServerRequest):
    """管理MCP服务器"""
    try:
        logger.info(f"收到MCP服务器管理请求: {request.action}")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        if request.action == "start":
            success = fbx_exporter.start_mcp_server()
            message = "MCP服务器启动成功" if success else "MCP服务器启动失败"
        elif request.action == "stop":
            fbx_exporter.stop_mcp_server()
            success = True
            message = "MCP服务器已停止"
        elif request.action == "status":
            success = True
            message = "MCP状态获取成功"
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {request.action}")

        # 获取当前状态
        mcp_status = fbx_exporter.get_mcp_status()

        return MCPServerResponse(
            success=success,
            action=request.action,
            server_running=mcp_status.get("server_running", False),
            message=message,
            config=mcp_status.get("config")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP服务器管理失败: {e}")
        raise HTTPException(status_code=500, detail=f"MCP服务器管理失败: {str(e)}")


@app.get("/scene-info", response_model=SceneInfoResponse)
async def get_scene_info():
    """获取Blender场景信息"""
    try:
        logger.info("获取场景信息")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        scene_info = fbx_exporter.get_scene_info()

        if "error" in scene_info:
            return SceneInfoResponse(
                success=False,
                error_message=scene_info["error"]
            )
        else:
            return SceneInfoResponse(
                success=True,
                scene_info=scene_info
            )

    except Exception as e:
        logger.error(f"获取场景信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取场景信息失败: {str(e)}")


@app.get("/scene-screenshot")
async def get_scene_screenshot():
    """获取场景截图"""
    try:
        logger.info("获取场景截图")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        screenshot_data = fbx_exporter.get_scene_screenshot()

        if screenshot_data:
            from fastapi.responses import Response
            return Response(
                content=screenshot_data,
                media_type="image/png",
                headers={"Content-Disposition": "inline; filename=scene_screenshot.png"}
            )
        else:
            raise HTTPException(status_code=404, detail="无法获取场景截图")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取场景截图失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取场景截图失败: {str(e)}")


@app.post("/execute-blender-code", response_model=BlenderCodeResponse)
async def execute_blender_code(request: BlenderCodeRequest):
    """执行Blender代码"""
    try:
        logger.info("执行Blender代码")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        # 检查MCP状态
        mcp_status = fbx_exporter.get_mcp_status()
        if not mcp_status.get("enabled") or not mcp_status.get("server_running"):
            raise HTTPException(
                status_code=400,
                detail="MCP服务器未运行，无法执行Blender代码"
            )

        # 通过MCP执行代码
        mcp_manager = get_mcp_manager()
        result = mcp_manager.execute_blender_code(request.code)

        return BlenderCodeResponse(
            success=result.get("success", False),
            result=result.get("result"),
            error_message=result.get("error")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行Blender代码失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行Blender代码失败: {str(e)}")


@app.post("/manage-addon", response_model=AddonManagementResponse)
async def manage_addon(request: AddonManagementRequest):
    """管理Blender插件"""
    try:
        logger.info(f"收到插件管理请求: {request.action}")

        if addon_manager is None:
            raise HTTPException(status_code=500, detail="插件管理器未初始化")

        if request.action == "install":
            result = addon_manager.install_addon(request.target_dir)
            success = result.get("success", False)
            message = "插件安装成功" if success else f"插件安装失败: {result.get('error', '未知错误')}"

        elif request.action == "uninstall":
            result = addon_manager.uninstall_addon()
            success = result.get("success", False)
            message = "插件卸载成功" if success else f"插件卸载失败: {result.get('error', '未知错误')}"

        elif request.action == "status":
            result = addon_manager.check_addon_status()
            success = True
            installed = result.get("installed", False)
            enabled = result.get("enabled", False)
            message = f"插件状态: {'已安装' if installed else '未安装'}, {'已启用' if enabled else '未启用'}"

        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {request.action}")

        return AddonManagementResponse(
            success=success,
            action=request.action,
            message=message,
            details=result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"插件管理失败: {e}")
        raise HTTPException(status_code=500, detail=f"插件管理失败: {str(e)}")


@app.get("/addon-installation-guide")
async def get_addon_installation_guide():
    """获取插件安装指南"""
    try:
        if addon_manager is None:
            raise HTTPException(status_code=500, detail="插件管理器未初始化")

        guide = addon_manager.get_installation_guide()
        return guide

    except Exception as e:
        logger.error(f"获取安装指南失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取安装指南失败: {str(e)}")


if __name__ == "__main__":
    # 开发模式运行
    uvicorn.run(
        "backend.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )