# Motion Agent - MCP Enhanced Animation System

🎬 **AI-powered animation generation with real-time Blender integration via Model Context Protocol (MCP)**

Motion Agent is an advanced system that converts natural language descriptions into professional-quality 3D animations. Enhanced with MCP integration, it provides real-time communication with Blender for sophisticated animation generation and live scene monitoring.

## 🌟 What's New: MCP Integration

Motion Agent now features **Model Context Protocol (MCP) integration**, bringing powerful new capabilities:

- **🔄 Real-time Blender Communication**: Live bidirectional communication with Blender
- **📊 Live Scene Monitoring**: Real-time scene information and object tracking
- **📸 Viewport Screenshots**: Capture Blender viewport images on demand
- **🎯 Enhanced Animation Generation**: More sophisticated animation operations
- **🔧 Automatic Addon Management**: Seamless Blender addon installation
- **⚡ Advanced Code Execution**: Execute custom Python code in Blender

## 🎯 Features

### Core Features
- **Natural Language to Animation**: Convert text descriptions into game action commands
- **27 Game Action Types**: Comprehensive set of character actions (move, jump, attack, etc.)
- **Blender Integration**: Automatic FBX export with proper animation data
- **3D Preview**: Real-time preview of generated animations using React Three Fiber
- **REST API**: Complete API for integration with other systems
- **Configurable**: Flexible configuration for different use cases

### MCP Enhanced Features
- **Real-time Scene Information**: Live access to Blender scene data
- **Advanced Animation Control**: MCP-powered sophisticated animation generation
- **Live Viewport Capture**: Real-time screenshot capabilities
- **Dynamic Code Execution**: Execute custom Blender operations
- **Automatic Addon Management**: Seamless plugin installation and management
- **Enhanced Error Handling**: Better feedback and debugging capabilities

## 🚀 Quick Start

### Standard Setup
```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Install dependencies
pip install -e .

# Set up environment
export OPENAI_API_KEY="your-api-key"

# Start backend
cd backend
uvicorn app:app --reload --host 0.0.0.0 --port 8000

# Start frontend (in another terminal)
cd frontend
npm install
npm run dev
```

### MCP Quick Start
```bash
# 1. Verify MCP is enabled
curl http://localhost:8000/health | jq '.mcp_status'

# 2. Start MCP server
curl -X POST http://localhost:8000/mcp-server \
  -H "Content-Type: application/json" \
  -d '{"action": "start"}'

# 3. Install Blender addon
curl -X POST http://localhost:8000/manage-addon \
  -H "Content-Type: application/json" \
  -d '{"action": "install"}'

# 4. Test MCP features
curl http://localhost:8000/scene-info
curl http://localhost:8000/scene-screenshot -o screenshot.png
```

**📚 For detailed setup instructions, see [MCP Quick Start Guide](./MCP_QUICK_START.md)**

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │    Blender      │
│                 │    │                 │    │                 │
│ • React Three   │◄──►│ • FastAPI       │◄──►│ • MCP Server    │
│ • MCP Controls  │    │ • LangChain     │    │ • Addon         │
│ • Live Preview  │    │ • MCP Manager   │    │ • Animation     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### MCP Architecture

```
Motion Agent Backend ◄──► MCP Server ◄──► Blender Addon
     │                      │                    │
     ├─ Action Conversion    ├─ Scene Info       ├─ Code Execution
     ├─ FBX Export          ├─ Screenshots      ├─ Animation Control
     └─ API Endpoints       └─ Live Monitoring  └─ Object Management
```

## 📖 Documentation

- **[MCP Integration Guide](./MCP_INTEGRATION_GUIDE.md)** - Comprehensive MCP documentation
- **[MCP Quick Start](./MCP_QUICK_START.md)** - 5-minute setup guide
- **[Implementation Details](./MOTION_AGENT_IMPLEMENTATION.md)** - Technical implementation
- **[API Reference](#-api-reference)** - Complete API documentation

## 🔧 Configuration

### Basic Configuration
```python
# backend/config.py
BLENDER_EXECUTABLE = "/path/to/blender"  # Auto-detected if not set
OPENAI_API_KEY = "your-api-key"
```

### MCP Configuration
```json
{
  "mcp_settings": {
    "enabled": true,
    "server_host": "localhost",
    "server_port": 9876,
    "auto_start_server": true,
    "addon_auto_install": true,
    "features": {
      "scene_info": true,
      "viewport_screenshot": true,
      "code_execution": true,
      "advanced_operations": true
    }
  }
}
```

## 🎮 Usage Examples

### Basic Animation Generation
```python
import requests

# Generate actions from natural language
response = requests.post("http://localhost:8000/generate-actions", json={
    "prompt": "Character walks forward, then jumps over an obstacle",
    "character_name": "Hero"
})

actions = response.json()["actions"]

# Export to FBX with MCP enhancement
export_response = requests.post("http://localhost:8000/export-fbx", json={
    "actions": actions,
    "project_name": "HeroAnimation",
    "character_name": "Hero"
})
```

### MCP Enhanced Operations
```python
# Get real-time scene information
scene_info = requests.get("http://localhost:8000/scene-info").json()
print(f"Scene: {scene_info['scene_info']['name']}")
print(f"Objects: {scene_info['scene_info']['object_count']}")

# Execute custom Blender code
code_response = requests.post("http://localhost:8000/execute-blender-code", json={
    "code": """
import bpy
# Create a custom animation
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 1))
cube = bpy.context.active_object
cube.keyframe_insert(data_path="location", frame=1)
cube.location.z = 3
cube.keyframe_insert(data_path="location", frame=50)
print(f"Created animation for {cube.name}")
"""
})

# Capture viewport screenshot
screenshot = requests.get("http://localhost:8000/scene-screenshot")
with open("scene.png", "wb") as f:
    f.write(screenshot.content)
```

## 🌐 API Reference

### Standard Endpoints
- `POST /generate-actions` - Generate actions from natural language
- `POST /export-fbx` - Export FBX animation file
- `GET /health` - System health check
- `GET /output-files` - List generated files

### MCP Endpoints
- `POST /mcp-server` - Manage MCP server (start/stop/status)
- `GET /scene-info` - Get real-time Blender scene information
- `GET /scene-screenshot` - Capture Blender viewport screenshot
- `POST /execute-blender-code` - Execute Python code in Blender
- `POST /manage-addon` - Install/uninstall/check Blender addon
- `GET /addon-installation-guide` - Get addon installation instructions

## 🧪 Testing

### Run Tests
```bash
# Run all tests
pytest

# Run MCP integration tests
pytest backend/tests/test_mcp_integration.py -v

# Run with coverage
pytest --cov=backend --cov-report=html
```

### Manual Testing
```bash
# Test MCP functionality
python -m backend.tests.test_mcp_integration

# Test API endpoints
curl http://localhost:8000/health
curl -X POST http://localhost:8000/mcp-server -d '{"action": "status"}'
```

## 🔍 Troubleshooting

### Common Issues

**MCP Server Won't Start**
```bash
# Check Blender installation
blender --version

# Check port availability
netstat -an | grep 9876

# View detailed logs
curl http://localhost:8000/health | jq '.mcp_status'
```

**Addon Installation Fails**
```bash
# Check addon status
curl -X POST http://localhost:8000/manage-addon -d '{"action": "status"}'

# Get installation guide
curl http://localhost:8000/addon-installation-guide
```

**For more troubleshooting, see [MCP Integration Guide](./MCP_INTEGRATION_GUIDE.md#troubleshooting)**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup
```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests before committing
pytest
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[blender-mcp](https://github.com/ahujasid/blender-mcp)** - Original MCP implementation for Blender
- **Model Context Protocol** - Standardized protocol for AI tool integration
- **Blender Foundation** - Amazing 3D creation software
- **React Three Fiber** - Excellent React 3D library
- **LangChain** - Powerful LLM framework

## 📊 Project Status

- ✅ Core animation generation
- ✅ Blender FBX export
- ✅ MCP server integration
- ✅ Real-time scene monitoring
- ✅ Automatic addon management
- ✅ Frontend MCP controls
- 🚧 Asset library integration (planned)
- 🚧 Advanced material systems (planned)
- 🚧 Collaborative features (planned)

---

**🎬 Ready to create amazing animations with AI and real-time Blender integration? Get started with our [Quick Start Guide](./MCP_QUICK_START.md)!**