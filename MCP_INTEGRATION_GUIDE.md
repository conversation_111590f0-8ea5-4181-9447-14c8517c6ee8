# Motion Agent MCP Integration Guide

## Overview

This guide covers the integration of the Model Context Protocol (MCP) with Motion Agent, enabling enhanced Blender operations and real-time communication between the Motion Agent system and Blender.

## What is MCP?

The Model Context Protocol (MCP) is a standardized protocol that enables AI assistants to securely connect to external data sources and tools. In our case, it provides a bridge between Motion Agent and Blender, allowing for:

- Real-time scene information retrieval
- Advanced animation operations
- Live viewport screenshots
- Dynamic code execution in Blender
- Enhanced error handling and feedback

## Architecture

### Components

1. **MCP Server Manager** (`backend/blender/mcp_server.py`)
   - Manages MCP server lifecycle
   - Handles connections and communication
   - Provides high-level API for Blender operations

2. **MCP Enhanced Converter** (`backend/blender/mcp_enhanced_converter.py`)
   - Extends the basic action converter with MCP capabilities
   - Generates more sophisticated animations
   - Provides real-time feedback during conversion

3. **Blender Addon Manager** (`backend/blender/addon_manager.py`)
   - Automates Blender addon installation and management
   - Handles addon lifecycle operations
   - Provides status monitoring

4. **Blender MCP Addon** (`backend/blender/blender_mcp_addon.py`)
   - Runs inside Blender as a plugin
   - Provides MCP server functionality within Blender
   - Handles incoming commands and executes operations

5. **Enhanced Frontend Components**
   - `MCPEnhancedPreview`: 3D preview with MCP features
   - `MCPControlPanel`: MCP server management interface

## Installation and Setup

### Prerequisites

- Blender 3.0+ installed and accessible
- Python 3.8+ with required dependencies
- Motion Agent backend running

### Step 1: Install Dependencies

The MCP dependencies are automatically included in the project's `pyproject.toml`:

```toml
dependencies = [
    # ... other dependencies
    "mcp[cli]>=1.3.0",
    "requests>=2.31.0",
]
```

### Step 2: Configure MCP Settings

MCP settings are managed through the Blender configuration system:

```python
from backend.blender.blender_config import BlenderConfig

config = BlenderConfig()

# Enable MCP
config.update_mcp_settings({
    "enabled": True,
    "server_host": "localhost",
    "server_port": 9876,
    "auto_start_server": True,
    "addon_auto_install": True
})

# Configure features
config.set_mcp_feature("scene_info", True)
config.set_mcp_feature("viewport_screenshot", True)
config.set_mcp_feature("code_execution", True)
```

### Step 3: Start the MCP Server

#### Automatic Startup (Recommended)

The MCP server can be automatically started when the Motion Agent backend starts:

```python
# In your application startup
from backend.blender.mcp_server import get_mcp_manager

mcp_manager = get_mcp_manager()
if mcp_manager.start_server():
    print("MCP server started successfully")
```

#### Manual Startup via API

```bash
curl -X POST http://localhost:8000/mcp-server \
  -H "Content-Type: application/json" \
  -d '{"action": "start"}'
```

### Step 4: Install Blender Addon

#### Automatic Installation

```bash
curl -X POST http://localhost:8000/manage-addon \
  -H "Content-Type: application/json" \
  -d '{"action": "install"}'
```

#### Manual Installation

1. Copy `backend/blender/blender_mcp_addon.py` to your Blender addons directory
2. Enable the addon in Blender: Edit > Preferences > Add-ons
3. Search for "Motion Agent MCP" and enable it

## Usage

### Basic MCP Operations

#### Get Scene Information

```python
from backend.blender.mcp_server import get_mcp_manager

mcp_manager = get_mcp_manager()
scene_info = mcp_manager.get_scene_info()
print(f"Scene: {scene_info['name']}")
print(f"Objects: {scene_info['object_count']}")
```

#### Execute Blender Code

```python
code = """
import bpy

# Create a cube
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 1))
cube = bpy.context.active_object
cube.name = "MyCube"

print(f"Created cube: {cube.name}")
"""

result = mcp_manager.execute_blender_code(code)
if result["success"]:
    print("Code executed successfully")
    print(result["result"])
```

#### Capture Viewport Screenshot

```python
screenshot_data = mcp_manager.get_viewport_screenshot()
if screenshot_data:
    with open("screenshot.png", "wb") as f:
        f.write(screenshot_data)
```

### Enhanced Animation Conversion

The MCP Enhanced Converter provides more sophisticated animation generation:

```python
from backend.blender.mcp_enhanced_converter import MCPEnhancedConverter

# Create converter with MCP enabled
converter = MCPEnhancedConverter(use_mcp=True)

# Convert actions with enhanced features
actions = [
    {
        "action": "move",
        "params": {
            "direction": "forward",
            "speed": 1.5,
            "duration": 2.0
        }
    },
    {
        "action": "jump",
        "params": {
            "height": 2.0,
            "distance": 1.5,
            "style": "athletic"
        }
    }
]

project = converter.convert_actions_to_blender(actions, "EnhancedAnimation")
```

### Frontend Integration

#### Using the MCP Enhanced Preview Component

```tsx
import MCPEnhancedPreview from '@/components/mcp-enhanced-preview';

function AnimationPage() {
  return (
    <MCPEnhancedPreview
      fbxUrl="/models/animation.fbx"
      animationName="Enhanced Animation"
      enableMCPFeatures={true}
      apiBaseUrl="http://localhost:8000"
      showGrid={true}
      showStats={true}
    />
  );
}
```

#### Using the MCP Control Panel

```tsx
import MCPControlPanel from '@/components/mcp-control-panel';

function ControlPage() {
  const handleStatusChange = (status) => {
    console.log('MCP Status:', status);
  };

  return (
    <MCPControlPanel
      apiBaseUrl="http://localhost:8000"
      onStatusChange={handleStatusChange}
    />
  );
}
```

## API Endpoints

### MCP Server Management

#### POST /mcp-server
Manage MCP server lifecycle.

**Request:**
```json
{
  "action": "start" | "stop" | "status"
}
```

**Response:**
```json
{
  "success": true,
  "action": "start",
  "server_running": true,
  "message": "MCP server started successfully",
  "config": {
    "host": "localhost",
    "port": 9876,
    "timeout": 15.0
  }
}
```

### Scene Information

#### GET /scene-info
Get current Blender scene information.

**Response:**
```json
{
  "success": true,
  "scene_info": {
    "name": "Scene",
    "object_count": 5,
    "objects": [
      {
        "name": "Cube",
        "type": "MESH",
        "location": [0, 0, 0],
        "visible": true
      }
    ],
    "materials_count": 2,
    "frame_current": 1,
    "frame_start": 1,
    "frame_end": 250
  }
}
```

#### GET /scene-screenshot
Capture viewport screenshot.

**Response:** PNG image data

### Code Execution

#### POST /execute-blender-code
Execute Python code in Blender.

**Request:**
```json
{
  "code": "import bpy; print('Hello from Blender')"
}
```

**Response:**
```json
{
  "success": true,
  "result": "Hello from Blender\n"
}
```

### Addon Management

#### POST /manage-addon
Manage Blender addon.

**Request:**
```json
{
  "action": "install" | "uninstall" | "status",
  "target_dir": "/optional/target/directory"
}
```

**Response:**
```json
{
  "success": true,
  "action": "install",
  "message": "插件安装成功",
  "details": {
    "installed": true,
    "enabled": true,
    "target_path": "/path/to/addon.py"
  }
}
```

## Configuration

### MCP Settings

The MCP configuration is stored in the Blender configuration file and includes:

```json
{
  "mcp_settings": {
    "enabled": true,
    "server_host": "localhost",
    "server_port": 9876,
    "auto_start_server": true,
    "addon_auto_install": true,
    "addon_path": null,
    "connection_timeout": 15.0,
    "max_retries": 3,
    "features": {
      "scene_info": true,
      "viewport_screenshot": true,
      "code_execution": true,
      "advanced_operations": true,
      "asset_integration": false,
      "polyhaven_integration": false,
      "sketchfab_integration": false,
      "hyper3d_integration": false
    }
  }
}
```

### Environment Variables

- `BLENDER_EXECUTABLE`: Path to Blender executable (optional, auto-detected)
- `MCP_SERVER_HOST`: MCP server host (default: localhost)
- `MCP_SERVER_PORT`: MCP server port (default: 9876)

## Troubleshooting

### Common Issues

1. **MCP Server Won't Start**
   - Check if Blender is installed and accessible
   - Verify port 9876 is not in use
   - Check logs for specific error messages

2. **Addon Installation Fails**
   - Ensure Blender addon directory is writable
   - Check if Blender is running
   - Verify addon file exists and is valid

3. **Connection Timeouts**
   - Increase connection timeout in configuration
   - Check network connectivity
   - Verify MCP server is running

4. **Code Execution Errors**
   - Validate Python syntax
   - Check Blender API compatibility
   - Review error messages in logs

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger('backend.blender.mcp_server').setLevel(logging.DEBUG)
```

### Health Check

Use the health endpoint to verify system status:

```bash
curl http://localhost:8000/health
```

## Performance Considerations

- MCP operations add network overhead
- Large scene data transfers may be slow
- Screenshot capture can be resource-intensive
- Consider caching for frequently accessed data

## Security Notes

- MCP server runs on localhost by default
- Code execution should be restricted in production
- Validate all input parameters
- Monitor resource usage

## Future Enhancements

- Asset library integration (PolyHaven, Sketchfab)
- Advanced material systems
- Real-time collaboration features
- Performance optimizations
- Extended Blender API coverage
