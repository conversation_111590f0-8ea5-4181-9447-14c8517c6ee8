# Motion Agent MCP Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### Prerequisites
- ✅ Blender 3.0+ installed
- ✅ Motion Agent backend running
- ✅ Python 3.8+ environment

### Step 1: Verify Installation
Check if MCP dependencies are installed:
```bash
# From the project root
pip list | grep mcp
```

### Step 2: Start the Backend
```bash
cd backend
uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### Step 3: Check System Health
```bash
curl http://localhost:8000/health
```

Look for these indicators in the response:
```json
{
  "components": {
    "mcp_enabled": true,
    "blender": true
  },
  "mcp_status": {
    "enabled": true
  }
}
```

### Step 4: Start MCP Server
```bash
curl -X POST http://localhost:8000/mcp-server \
  -H "Content-Type: application/json" \
  -d '{"action": "start"}'
```

### Step 5: Install Blender Addon
```bash
curl -X POST http://localhost:8000/manage-addon \
  -H "Content-Type: application/json" \
  -d '{"action": "install"}'
```

### Step 6: Test MCP Features
```bash
# Get scene information
curl http://localhost:8000/scene-info

# Capture screenshot
curl http://localhost:8000/scene-screenshot -o screenshot.png
```

## 🎯 Quick Test

### Test 1: Basic Scene Info
```python
import requests

response = requests.get("http://localhost:8000/scene-info")
if response.status_code == 200:
    data = response.json()
    if data["success"]:
        print(f"✅ Scene: {data['scene_info']['name']}")
        print(f"✅ Objects: {data['scene_info']['object_count']}")
    else:
        print(f"❌ Error: {data['error_message']}")
```

### Test 2: Execute Simple Code
```python
import requests

code = """
import bpy
print(f"Blender version: {bpy.app.version_string}")
print(f"Current scene: {bpy.context.scene.name}")
"""

response = requests.post(
    "http://localhost:8000/execute-blender-code",
    json={"code": code}
)

if response.status_code == 200:
    data = response.json()
    if data["success"]:
        print("✅ Code executed successfully:")
        print(data["result"])
    else:
        print(f"❌ Error: {data['error_message']}")
```

### Test 3: Enhanced Animation
```python
import requests

# Generate actions using natural language
actions_response = requests.post(
    "http://localhost:8000/generate-actions",
    json={
        "prompt": "Character walks forward for 2 seconds, then jumps high",
        "character_name": "TestCharacter"
    }
)

if actions_response.status_code == 200:
    actions_data = actions_response.json()
    
    # Export with MCP enhancement
    export_response = requests.post(
        "http://localhost:8000/export-fbx",
        json={
            "actions": actions_data["actions"],
            "project_name": "MCPTest",
            "character_name": "TestCharacter"
        }
    )
    
    if export_response.status_code == 200:
        export_data = export_response.json()
        if export_data["export_success"]:
            print(f"✅ FBX exported: {export_data['output_file_path']}")
        else:
            print(f"❌ Export failed: {export_data['error_message']}")
```

## 🎨 Frontend Quick Test

### Start Frontend
```bash
cd frontend
npm run dev
```

Visit `http://localhost:3000` and:

1. ✅ Check MCP status indicator (green dot = connected)
2. ✅ Click "Start MCP" if not running
3. ✅ View real-time scene information in the side panel
4. ✅ Click "Capture Screenshot" to test viewport capture
5. ✅ Toggle MCP features on/off to compare

## 🔧 Troubleshooting

### Issue: MCP Server Won't Start
```bash
# Check if Blender is accessible
blender --version

# Check port availability
netstat -an | grep 9876

# Check logs
curl http://localhost:8000/health | jq '.mcp_status'
```

### Issue: Addon Installation Fails
```bash
# Check addon status
curl -X POST http://localhost:8000/manage-addon \
  -H "Content-Type: application/json" \
  -d '{"action": "status"}'

# Get installation guide
curl http://localhost:8000/addon-installation-guide
```

### Issue: Scene Info Returns Error
```bash
# Verify MCP server is running
curl -X POST http://localhost:8000/mcp-server \
  -H "Content-Type: application/json" \
  -d '{"action": "status"}'

# Check if Blender is responding
curl -X POST http://localhost:8000/execute-blender-code \
  -H "Content-Type: application/json" \
  -d '{"code": "print(\"Hello from Blender\")"}'
```

## 📊 Status Indicators

### ✅ Everything Working
- Health check shows all components as `true`
- MCP server status: `"server_running": true`
- Scene info returns valid data
- Screenshot capture works
- Code execution succeeds

### ⚠️ Partial Functionality
- MCP enabled but server not running → Start server
- Addon installed but not enabled → Check Blender preferences
- Some features disabled → Check configuration

### ❌ Not Working
- Blender not detected → Install/configure Blender
- MCP disabled → Enable in configuration
- Connection errors → Check network/firewall

## 🎯 Next Steps

Once basic functionality is working:

1. **Explore Advanced Features**
   - Try complex animation sequences
   - Test real-time scene monitoring
   - Experiment with custom Blender code

2. **Customize Configuration**
   - Adjust MCP server settings
   - Enable/disable specific features
   - Configure addon preferences

3. **Integrate with Your Workflow**
   - Use MCP APIs in your applications
   - Build custom frontend components
   - Extend with additional Blender operations

## 📚 Resources

- [Full MCP Integration Guide](./MCP_INTEGRATION_GUIDE.md)
- [API Documentation](./backend/app.py) - See endpoint definitions
- [Frontend Components](./frontend/src/components/) - MCP-enhanced components
- [Test Suite](./backend/tests/test_mcp_integration.py) - Integration tests

## 🆘 Getting Help

If you encounter issues:

1. Check the [Troubleshooting](#-troubleshooting) section above
2. Review logs in the backend console
3. Test individual components using the curl commands
4. Verify your Blender installation and accessibility

## 🎉 Success Checklist

- [ ] Backend health check passes
- [ ] MCP server starts successfully
- [ ] Blender addon installs and enables
- [ ] Scene info retrieval works
- [ ] Screenshot capture functions
- [ ] Code execution succeeds
- [ ] Frontend shows MCP status
- [ ] Enhanced animation export works

Once all items are checked, you have a fully functional MCP-enhanced Motion Agent system!
