'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Environment, Grid, Stats } from '@react-three/drei';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import * as THREE from 'three';

interface MCPEnhancedPreviewProps {
  fbxUrl?: string;
  animationName?: string;
  autoPlay?: boolean;
  showGrid?: boolean;
  showStats?: boolean;
  cameraPosition?: [number, number, number];
  backgroundColor?: string;
  enableMCPFeatures?: boolean;
  apiBaseUrl?: string;
}

interface SceneInfo {
  name: string;
  object_count: number;
  objects: Array<{
    name: string;
    type: string;
    location: [number, number, number];
    visible: boolean;
  }>;
  materials_count: number;
  frame_current?: number;
  frame_start?: number;
  frame_end?: number;
}

interface MCPStatus {
  enabled: boolean;
  server_running: boolean;
  config?: any;
}

function FBXModel({ 
  fbxUrl, 
  autoPlay = true 
}: { 
  fbxUrl: string; 
  autoPlay?: boolean; 
}) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load FBX model
  useEffect(() => {
    console.log('Starting to load FBX from:', fbxUrl);
    const loader = new FBXLoader();

    loader.load(
      fbxUrl,
      (fbx) => {
        try {
          console.log('FBX loaded successfully:', fbx);
          console.log('Animations found:', fbx.animations.length);

          // Scale the model appropriately
          fbx.scale.setScalar(0.01);

          // Center the model
          const box = new THREE.Box3().setFromObject(fbx);
          const center = box.getCenter(new THREE.Vector3());
          fbx.position.sub(center);

          // Set up animations
          if (fbx.animations && fbx.animations.length > 0) {
            const mixer = new THREE.AnimationMixer(fbx);
            mixerRef.current = mixer;

            // Play the first animation by default
            if (autoPlay) {
              const action = mixer.clipAction(fbx.animations[0]);
              action.play();
              console.log('Animation started playing');
            }

            setAnimations(fbx.animations);
          } else {
            console.log('No animations found in FBX file');
          }

          setModel(fbx);
          setIsLoading(false);
        } catch (err) {
          console.error('Error processing FBX model:', err);
          setError('Failed to process FBX model');
          setIsLoading(false);
        }
      },
      (progress) => {
        console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%');
      },
      (error) => {
        console.error('Error loading FBX:', error);
        setError('Failed to load FBX file: ' + error.message);
        setIsLoading(false);
      }
    );

    return () => {
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
      }
    };
  }, [fbxUrl, autoPlay]);

  // Animation loop
  useFrame((state, delta) => {
    if (mixerRef.current) {
      mixerRef.current.update(delta);
    }
  });

  if (isLoading) {
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="gray" wireframe />
      </mesh>
    );
  }

  if (error || !model) {
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="red" />
      </mesh>
    );
  }

  return (
    <group ref={groupRef}>
      <primitive object={model} />
    </group>
  );
}

export default function MCPEnhancedPreview({
  fbxUrl,
  animationName = "Animation",
  autoPlay = true,
  showGrid = true,
  showStats = false,
  cameraPosition = [5, 5, 5],
  backgroundColor = "#f0f0f0",
  enableMCPFeatures = true,
  apiBaseUrl = "http://localhost:8000"
}: MCPEnhancedPreviewProps) {
  const [mcpStatus, setMcpStatus] = useState<MCPStatus | null>(null);
  const [sceneInfo, setSceneInfo] = useState<SceneInfo | null>(null);
  const [sceneScreenshot, setSceneScreenshot] = useState<string | null>(null);
  const [isLoadingMCP, setIsLoadingMCP] = useState(false);
  const [mcpError, setMcpError] = useState<string | null>(null);

  // Fetch MCP status
  const fetchMCPStatus = useCallback(async () => {
    if (!enableMCPFeatures) return;

    try {
      const response = await fetch(`${apiBaseUrl}/mcp-server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'status' }),
      });

      if (response.ok) {
        const data = await response.json();
        setMcpStatus({
          enabled: true,
          server_running: data.server_running,
          config: data.config
        });
      } else {
        setMcpStatus({ enabled: false, server_running: false });
      }
    } catch (error) {
      console.error('Failed to fetch MCP status:', error);
      setMcpStatus({ enabled: false, server_running: false });
    }
  }, [enableMCPFeatures, apiBaseUrl]);

  // Fetch scene info
  const fetchSceneInfo = useCallback(async () => {
    if (!enableMCPFeatures || !mcpStatus?.server_running) return;

    setIsLoadingMCP(true);
    try {
      const response = await fetch(`${apiBaseUrl}/scene-info`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSceneInfo(data.scene_info);
          setMcpError(null);
        } else {
          setMcpError(data.error_message || 'Failed to get scene info');
        }
      } else {
        setMcpError('Failed to fetch scene info');
      }
    } catch (error) {
      console.error('Failed to fetch scene info:', error);
      setMcpError('Network error while fetching scene info');
    } finally {
      setIsLoadingMCP(false);
    }
  }, [enableMCPFeatures, mcpStatus?.server_running, apiBaseUrl]);

  // Fetch scene screenshot
  const fetchSceneScreenshot = useCallback(async () => {
    if (!enableMCPFeatures || !mcpStatus?.server_running) return;

    try {
      const response = await fetch(`${apiBaseUrl}/scene-screenshot`);
      if (response.ok) {
        const blob = await response.blob();
        const imageUrl = URL.createObjectURL(blob);
        setSceneScreenshot(imageUrl);
      } else {
        console.error('Failed to fetch scene screenshot');
      }
    } catch (error) {
      console.error('Failed to fetch scene screenshot:', error);
    }
  }, [enableMCPFeatures, mcpStatus?.server_running, apiBaseUrl]);

  // Start MCP server
  const startMCPServer = useCallback(async () => {
    if (!enableMCPFeatures) return;

    try {
      const response = await fetch(`${apiBaseUrl}/mcp-server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'start' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMcpStatus({
            enabled: true,
            server_running: data.server_running,
            config: data.config
          });
        }
      }
    } catch (error) {
      console.error('Failed to start MCP server:', error);
    }
  }, [enableMCPFeatures, apiBaseUrl]);

  // Initialize MCP features
  useEffect(() => {
    if (enableMCPFeatures) {
      fetchMCPStatus();
    }
  }, [enableMCPFeatures, fetchMCPStatus]);

  // Auto-refresh scene info when MCP server is running
  useEffect(() => {
    if (mcpStatus?.server_running) {
      fetchSceneInfo();
      const interval = setInterval(fetchSceneInfo, 5000); // Refresh every 5 seconds
      return () => clearInterval(interval);
    }
  }, [mcpStatus?.server_running, fetchSceneInfo]);

  return (
    <div className="w-full border border-gray-300 rounded-lg overflow-hidden">
      <div className="bg-gray-100 px-4 py-2 border-b border-gray-300 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">{animationName}</h3>
        
        {enableMCPFeatures && (
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              mcpStatus?.server_running ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span className="text-sm text-gray-600">
              MCP: {mcpStatus?.server_running ? 'Connected' : 'Disconnected'}
            </span>
            
            {!mcpStatus?.server_running && mcpStatus?.enabled && (
              <button
                onClick={startMCPServer}
                className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
              >
                Start MCP
              </button>
            )}
          </div>
        )}
      </div>

      <div className="flex">
        {/* 3D Preview */}
        <div className="flex-1 h-96 relative">
          {fbxUrl ? (
            <Canvas
              camera={{ 
                position: cameraPosition, 
                fov: 50,
                near: 0.1,
                far: 1000
              }}
              style={{ background: backgroundColor }}
            >
              {/* Lighting */}
              <ambientLight intensity={0.6} />
              <directionalLight 
                position={[10, 10, 5]} 
                intensity={1}
                castShadow
                shadow-mapSize-width={2048}
                shadow-mapSize-height={2048}
              />
              <pointLight position={[-10, -10, -10]} intensity={0.3} />

              {/* Environment */}
              <Environment preset="studio" />

              {/* Grid */}
              {showGrid && (
                <Grid 
                  args={[10, 10]} 
                  cellSize={1} 
                  cellThickness={0.5} 
                  cellColor="#6e6e6e" 
                  sectionSize={5} 
                  sectionThickness={1} 
                  sectionColor="#9d4b4b" 
                  fadeDistance={25} 
                  fadeStrength={1} 
                  followCamera={false} 
                  infiniteGrid={true}
                />
              )}
              
              {/* FBX Model */}
              <FBXModel fbxUrl={fbxUrl} autoPlay={autoPlay} />
              
              {/* Controls */}
              <OrbitControls 
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                minDistance={1}
                maxDistance={50}
              />
              
              {/* Stats */}
              {showStats && <Stats />}
            </Canvas>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <p className="text-gray-500">No FBX file provided</p>
            </div>
          )}
          
          {/* Loading overlay */}
          <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
            {fbxUrl ? 'FBX Animation Preview' : 'MCP Enhanced Preview'}
          </div>
        </div>

        {/* MCP Info Panel */}
        {enableMCPFeatures && (
          <div className="w-80 bg-gray-50 border-l border-gray-300 p-4 overflow-y-auto">
            <h4 className="font-semibold text-gray-800 mb-3">MCP Scene Info</h4>
            
            {mcpError && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3">
                {mcpError}
              </div>
            )}

            {isLoadingMCP && (
              <div className="text-gray-500 mb-3">Loading scene info...</div>
            )}

            {sceneInfo && (
              <div className="space-y-3">
                <div>
                  <strong>Scene:</strong> {sceneInfo.name}
                </div>
                <div>
                  <strong>Objects:</strong> {sceneInfo.object_count}
                </div>
                <div>
                  <strong>Materials:</strong> {sceneInfo.materials_count}
                </div>
                
                {sceneInfo.frame_current !== undefined && (
                  <div>
                    <strong>Frame:</strong> {sceneInfo.frame_current} / {sceneInfo.frame_end}
                  </div>
                )}

                {sceneInfo.objects && sceneInfo.objects.length > 0 && (
                  <div>
                    <strong>Objects:</strong>
                    <ul className="mt-1 space-y-1 text-sm">
                      {sceneInfo.objects.slice(0, 5).map((obj, index) => (
                        <li key={index} className="text-gray-600">
                          {obj.name} ({obj.type})
                        </li>
                      ))}
                      {sceneInfo.objects.length > 5 && (
                        <li className="text-gray-500">
                          ... and {sceneInfo.objects.length - 5} more
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {mcpStatus?.server_running && (
              <div className="mt-4 space-y-2">
                <button
                  onClick={fetchSceneInfo}
                  className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                  disabled={isLoadingMCP}
                >
                  Refresh Scene Info
                </button>
                
                <button
                  onClick={fetchSceneScreenshot}
                  className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                >
                  Capture Screenshot
                </button>
              </div>
            )}

            {sceneScreenshot && (
              <div className="mt-4">
                <h5 className="font-medium text-gray-700 mb-2">Scene Screenshot</h5>
                <img 
                  src={sceneScreenshot} 
                  alt="Scene Screenshot" 
                  className="w-full rounded border"
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
