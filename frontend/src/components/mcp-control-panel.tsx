'use client';

import React, { useState, useEffect, useCallback } from 'react';

interface MCPControlPanelProps {
  apiBaseUrl?: string;
  onStatusChange?: (status: MCPStatus) => void;
}

interface MCPStatus {
  enabled: boolean;
  server_running: boolean;
  config?: any;
  scene_info?: any;
}

interface AddonStatus {
  installed: boolean;
  enabled: boolean;
  addon_name: string;
  status_message: string;
  installed_paths?: string[];
}

export default function MCPControlPanel({
  apiBaseUrl = "http://localhost:8000",
  onStatusChange
}: MCPControlPanelProps) {
  const [mcpStatus, setMcpStatus] = useState<MCPStatus | null>(null);
  const [addonStatus, setAddonStatus] = useState<AddonStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
  }, []);

  // Fetch MCP status
  const fetchMCPStatus = useCallback(async () => {
    try {
      const response = await fetch(`${apiBaseUrl}/mcp-server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'status' }),
      });

      if (response.ok) {
        const data = await response.json();
        const status: MCPStatus = {
          enabled: true,
          server_running: data.server_running,
          config: data.config
        };
        setMcpStatus(status);
        onStatusChange?.(status);
        addLog(`MCP Status: ${data.server_running ? 'Running' : 'Stopped'}`);
      } else {
        const status: MCPStatus = { enabled: false, server_running: false };
        setMcpStatus(status);
        onStatusChange?.(status);
        addLog('MCP Status: Not available');
      }
    } catch (error) {
      console.error('Failed to fetch MCP status:', error);
      const status: MCPStatus = { enabled: false, server_running: false };
      setMcpStatus(status);
      onStatusChange?.(status);
      addLog('MCP Status: Connection failed');
    }
  }, [apiBaseUrl, onStatusChange, addLog]);

  // Fetch addon status
  const fetchAddonStatus = useCallback(async () => {
    try {
      const response = await fetch(`${apiBaseUrl}/manage-addon`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'status' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.details) {
          setAddonStatus(data.details);
          addLog(`Addon Status: ${data.details.installed ? 'Installed' : 'Not installed'}, ${data.details.enabled ? 'Enabled' : 'Disabled'}`);
        }
      }
    } catch (error) {
      console.error('Failed to fetch addon status:', error);
      addLog('Addon Status: Check failed');
    }
  }, [apiBaseUrl, addLog]);

  // Start MCP server
  const startMCPServer = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    addLog('Starting MCP server...');

    try {
      const response = await fetch(`${apiBaseUrl}/mcp-server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'start' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          addLog('MCP server started successfully');
          await fetchMCPStatus();
        } else {
          setError(data.message || 'Failed to start MCP server');
          addLog('Failed to start MCP server');
        }
      } else {
        setError('Failed to start MCP server');
        addLog('Failed to start MCP server');
      }
    } catch (error) {
      console.error('Failed to start MCP server:', error);
      setError('Network error while starting MCP server');
      addLog('Network error while starting MCP server');
    } finally {
      setIsLoading(false);
    }
  }, [apiBaseUrl, addLog, fetchMCPStatus]);

  // Stop MCP server
  const stopMCPServer = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    addLog('Stopping MCP server...');

    try {
      const response = await fetch(`${apiBaseUrl}/mcp-server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'stop' }),
      });

      if (response.ok) {
        const data = await response.json();
        addLog('MCP server stopped');
        await fetchMCPStatus();
      } else {
        setError('Failed to stop MCP server');
        addLog('Failed to stop MCP server');
      }
    } catch (error) {
      console.error('Failed to stop MCP server:', error);
      setError('Network error while stopping MCP server');
      addLog('Network error while stopping MCP server');
    } finally {
      setIsLoading(false);
    }
  }, [apiBaseUrl, addLog, fetchMCPStatus]);

  // Install addon
  const installAddon = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    addLog('Installing MCP addon...');

    try {
      const response = await fetch(`${apiBaseUrl}/manage-addon`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'install' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          addLog('MCP addon installed successfully');
          await fetchAddonStatus();
        } else {
          setError(data.message || 'Failed to install addon');
          addLog('Failed to install MCP addon');
        }
      } else {
        setError('Failed to install addon');
        addLog('Failed to install MCP addon');
      }
    } catch (error) {
      console.error('Failed to install addon:', error);
      setError('Network error while installing addon');
      addLog('Network error while installing addon');
    } finally {
      setIsLoading(false);
    }
  }, [apiBaseUrl, addLog, fetchAddonStatus]);

  // Initialize
  useEffect(() => {
    fetchMCPStatus();
    fetchAddonStatus();
    addLog('MCP Control Panel initialized');
  }, [fetchMCPStatus, fetchAddonStatus, addLog]);

  // Auto-refresh status
  useEffect(() => {
    const interval = setInterval(() => {
      fetchMCPStatus();
    }, 10000); // Refresh every 10 seconds

    return () => clearInterval(interval);
  }, [fetchMCPStatus]);

  return (
    <div className="bg-white border border-gray-300 rounded-lg p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">MCP Control Panel</h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* MCP Server Status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">MCP Server</h3>
        
        <div className="flex items-center space-x-4 mb-3">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              mcpStatus?.server_running ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span className="text-sm font-medium">
              Status: {mcpStatus?.server_running ? 'Running' : 'Stopped'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              mcpStatus?.enabled ? 'bg-blue-500' : 'bg-gray-400'
            }`}></div>
            <span className="text-sm font-medium">
              MCP: {mcpStatus?.enabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>

        <div className="flex space-x-2">
          {!mcpStatus?.server_running ? (
            <button
              onClick={startMCPServer}
              disabled={isLoading || !mcpStatus?.enabled}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Starting...' : 'Start Server'}
            </button>
          ) : (
            <button
              onClick={stopMCPServer}
              disabled={isLoading}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Stopping...' : 'Stop Server'}
            </button>
          )}
          
          <button
            onClick={fetchMCPStatus}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Refresh
          </button>
        </div>

        {mcpStatus?.config && (
          <div className="mt-3 p-3 bg-gray-100 rounded">
            <h4 className="font-medium text-gray-700 mb-2">Server Config</h4>
            <div className="text-sm text-gray-600">
              <div>Host: {mcpStatus.config.host}</div>
              <div>Port: {mcpStatus.config.port}</div>
              <div>Timeout: {mcpStatus.config.timeout}s</div>
            </div>
          </div>
        )}
      </div>

      {/* Addon Status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Blender Addon</h3>
        
        {addonStatus && (
          <div className="mb-3">
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  addonStatus.installed ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm font-medium">
                  Installed: {addonStatus.installed ? 'Yes' : 'No'}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  addonStatus.enabled ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm font-medium">
                  Enabled: {addonStatus.enabled ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-3">{addonStatus.status_message}</p>
          </div>
        )}

        <div className="flex space-x-2">
          <button
            onClick={installAddon}
            disabled={isLoading || addonStatus?.installed}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Installing...' : 'Install Addon'}
          </button>
          
          <button
            onClick={fetchAddonStatus}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Check Status
          </button>
        </div>
      </div>

      {/* Activity Log */}
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Activity Log</h3>
        <div className="bg-gray-100 rounded p-3 h-32 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500 text-sm">No activity yet...</p>
          ) : (
            <div className="space-y-1">
              {logs.map((log, index) => (
                <div key={index} className="text-sm text-gray-700 font-mono">
                  {log}
                </div>
              ))}
            </div>
          )}
        </div>
        
        <button
          onClick={() => setLogs([])}
          className="mt-2 px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
        >
          Clear Log
        </button>
      </div>
    </div>
  );
}
